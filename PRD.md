# Product Requirements Document (PRD): Holistic AI-Powered Code Quality Review for TypeScript/React/sal.js Project

## Overview
This document defines the requirements for an AI-driven code review process that begins with a holistic analysis of the entire codebase before making targeted improvements to individual components. The goal is to ensure code quality, consistency, maintainability, and architectural coherence, leveraging Context7 MCP as the authoritative reference.

## Objectives
- Achieve a comprehensive understanding of the codebase, including structure, dependencies, and patterns.
- Identify and eliminate redundant, duplicate, or inconsistent code across modules.
- Enforce consistent naming conventions and architectural patterns.
- Detect unused variables, functions, and imports.
- Highlight potential security, performance, and scalability issues.
- Suggest and implement refactoring opportunities and best practices.
- Provide actionable, context-aware feedback for maintainability and scalability.

## Features & Requirements

### 1. Codebase Preparation
- The entire codebase must be organized in a single repository.
- Exclude non-source files (build artifacts, logs, etc.) from the review process.

### 2. Initial Holistic Codebase Analysis
- The AI must first analyze the full codebase to:
  - Map out the structure, dependencies, and data flow.
  - Identify cross-cutting concerns, global patterns, and architectural issues.
  - Detect system-wide redundancies or inconsistencies.

### 3. Automated Static Analysis
- Integrate ESLint and Prettier for linting and formatting.
- Employ jscpd or similar tools to detect code duplication.

### 4. AI-Powered Review & Refactoring Process
- Based on the holistic analysis, the AI will:
  - Prioritize and plan improvements, starting with the most impactful areas.
  - Review and refactor individual components with full context of their role in the system.
  - For each component/module:
    - Check for redundant or duplicate code.
    - Ensure naming, structure, and patterns align with project-wide conventions.
    - Identify unused code (variables, functions, imports).
    - Flag security, performance, and scalability concerns.
    - Recommend and implement refactoring and best practices.
- **Whenever the AI is unclear on any subject, implementation detail, or wants to ensure best practices, it must reference Context7 MCP for up-to-date documentation and code samples.**
- The AI is encouraged to proactively use Context7 MCP as a source of truth and guidance for all ambiguous or complex topics.

### 5. Review Output
- The AI tool should provide:
  - A summary of system-wide and component-specific issues found.
  - Line-specific or section-specific comments.
  - Clear, actionable suggestions for improvement.
  - Example code snippets for suggested changes where appropriate.

### 6. Continuous Integration
- Integrate automated checks (lint, format, duplication detection) into CI pipelines.
- Ensure every pull request runs these checks and blocks merges if critical issues are detected.

### 7. Documentation & Reporting
- Maintain a changelog or report of issues found and resolved.
- Document adopted conventions and best practices for future reference.

## Success Criteria
- All code passes linting, formatting, and duplication checks.
- AI review identifies and resolves major redundancies, inconsistencies, and unused code at both system and component levels.
- The codebase is more maintainable, readable, scalable, and architecturally sound.
- Developers receive clear, actionable, context-aware feedback.
- The AI consistently refers to Context7 MCP for clarification and best practice implementation.

NOTE: ignore files and direcories in .gitignore from analysis