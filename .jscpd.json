{"threshold": 5, "reporters": ["html", "console", "badge"], "ignore": ["**/node_modules/**", "**/dist/**", "**/src-tauri/target/**", "**/src-tauri/gen/**", "**/src-html/**", "**/*.config.js", "**/*.config.ts", "**/*.d.ts"], "gitignore": true, "mode": "mild", "format": ["typescript", "tsx", "javascript", "jsx"], "output": "./reports/jscpd", "absolute": true, "noSymlinks": true, "skipLocal": true, "minLines": 5, "minTokens": 50, "maxLines": 500, "maxSize": "30kb", "formatsExts": {"typescript": ["ts"], "tsx": ["tsx"], "javascript": ["js"], "jsx": ["jsx"]}}