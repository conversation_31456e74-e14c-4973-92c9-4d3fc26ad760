{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "opener:default", "sql:default", "sql:allow-load", "sql:allow-execute", "sql:allow-select", "sql:allow-close", "dialog:default", "fs:default", "fs:allow-write-file", "fs:allow-write-text-file", "fs:allow-read-file", "fs:allow-read-text-file", "fs:allow-create", "fs:allow-exists", "fs:allow-mkdir", "fs:allow-remove", "fs:allow-app-write", "fs:allow-app-write-recursive", "fs:allow-app-read", "fs:allow-app-read-recursive"]}