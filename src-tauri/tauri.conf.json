{"$schema": "https://schema.tauri.app/config/2", "productName": "electixir", "version": "0.1.0", "identifier": "com.electixir.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "electixir", "width": 1400, "height": 890}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/icon.png"]}}