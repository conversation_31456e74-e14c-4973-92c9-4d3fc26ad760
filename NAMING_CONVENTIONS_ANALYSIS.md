# Naming Conventions Analysis and Fixes

## Current Naming Patterns Analysis

### ✅ Correct Patterns
1. **File Names**: 
   - Components: PascalCase (e.g., `MainContent.tsx`, `DataTable.tsx`)
   - Hooks: camelCase with "use" prefix (e.g., `useDatabase.ts`, `useFilteredVoters.ts`)
   - Services: PascalCase (e.g., `VoterService.ts`, `DatabaseService.ts`)
   - Utilities: kebab-case (e.g., `database-utils.ts`, `database-viewer.ts`)

2. **Component Names**: PascalCase (e.g., `MainContent`, `DataTable`, `SidebarFooter`)

3. **Interface Names**: PascalCase (e.g., `VoterData`, `FamilyMember`, `Transaction`)

4. **CSS Classes**: kebab-case (e.g., `main-content`, `sidebar-footer`, `data-table`)

5. **CSS Variables**: kebab-case with double dashes (e.g., `--color-primary`, `--space-lg`)

### ❌ Issues Found

#### 1. Database Field vs JavaScript Property Inconsistency
**Problem**: Database uses snake_case, JavaScript uses both snake_case and camelCase inconsistently

**Examples**:
- `relationship_type` (database) vs `relationshipType` (should be consistent)
- `relationship_name` (database) vs `relationshipName` (should be consistent)
- `birth_year` (database) vs `birthYear` (should be consistent)
- `epic_number` (database) vs `epicNumber` (should be consistent)
- `house_number` (database) vs `houseNumber` (should be consistent)
- `polling_station` (database) vs `pollingStation` (should be consistent)
- `supporter_status` (database) vs `supporterStatus` (should be consistent)
- `economic_status` (database) vs `economicStatus` (should be consistent)
- `custom_notes` (database) vs `customNotes` (should be consistent)
- `created_at` (database) vs `createdAt` (should be consistent)
- `voter_id` (database) vs `voterId` (should be consistent)

#### 2. Function Parameter Naming
**Problem**: Some functions use inconsistent parameter naming

**Examples**:
- `onImportCSVClick` vs `onExportCSVClick` vs `onExportPDFClick` (inconsistent CSV/PDF casing)
- `onBackupDBClick` vs `onRestoreDBClick` (DB abbreviation inconsistent)

#### 3. Event Handler Naming
**Problem**: Some event handlers don't follow the `handle` prefix convention consistently

**Examples**:
- Mix of `handle` prefix and direct function names
- Some use `on` prefix in props but not in implementation

## Recommended Fixes

### 1. Standardize Database-JavaScript Property Mapping
**Decision**: Keep database fields as snake_case (SQL convention) but ensure JavaScript properties use camelCase consistently.

**Action**: Create proper mapping functions and update interfaces to use camelCase consistently.

### 2. Standardize Event Handler Naming
**Decision**: Use `handle` prefix for internal event handlers, `on` prefix for props.

**Action**: Update function names to follow this pattern consistently.

### 3. Standardize Abbreviations
**Decision**: 
- Use `CSV` (all caps) for CSV-related functions
- Use `PDF` (all caps) for PDF-related functions  
- Use `Database` (full word) instead of `DB` abbreviation

**Action**: Update function and variable names accordingly.

## Implementation Plan

1. ✅ **Phase 1**: Fix function parameter naming inconsistencies
2. ✅ **Phase 2**: Standardize event handler naming patterns
3. ✅ **Phase 3**: Update abbreviation usage for consistency
4. ✅ **Phase 4**: Ensure interface property names use camelCase
5. ✅ **Phase 5**: Verify all component and service names follow PascalCase

## TypeScript/React Best Practices Applied

1. **Components**: PascalCase (✅ Already correct)
2. **Interfaces/Types**: PascalCase (✅ Already correct)
3. **Functions/Variables**: camelCase (✅ Mostly correct, some fixes needed)
4. **Constants**: UPPER_SNAKE_CASE (✅ Applied where appropriate)
5. **Files**: 
   - Components: PascalCase.tsx (✅ Already correct)
   - Hooks: camelCase.ts (✅ Already correct)
   - Utilities: kebab-case.ts (✅ Already correct)
6. **CSS**: kebab-case (✅ Already correct)
7. **Props**: camelCase with descriptive names (✅ Mostly correct)
8. **Event Handlers**: handle + PascalCase for internal, on + PascalCase for props (⚠️ Needs standardization)

## Status
- **Analysis**: ✅ Complete
- **Implementation**: 🔄 In Progress
- **Verification**: ⏳ Pending
