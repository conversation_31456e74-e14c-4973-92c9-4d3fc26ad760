/**
 * Shared type definitions for the application
 */

import { VoterStatus, SupporterStatus, Gender, RelationshipType, Theme } from '../utils/constants';

// Base voter data interface
export interface VoterData {
  id?: number;
  name: string;
  relationship_type?: RelationshipType;
  relationship_name?: string;
  gender: Gender;
  birth_year?: number;
  epic_number: string;
  house_number?: string;
  polling_station?: string;
  section?: string;

  // Extended contact information
  phone?: string;
  email?: string;
  facebook?: string;
  instagram?: string;
  twitter?: string;

  // Voter status
  status?: VoterStatus;

  // Political information
  supporter_status?: SupporterStatus;

  // Demographics
  education?: string;
  occupation?: string;
  community?: string;
  religion?: string;
  economic_status?: string;
  custom_notes?: string;
}

// Extended voter interface with computed fields
export interface Voter extends VoterData {
  age?: number;
  epic: string; // Alias for epic_number
  pollingStation: string; // Alias for polling_station
  economicStatus?: string; // Alias for economic_status
  fatherName?: string; // Alias for relationship_name when relationship_type is Father
  qualification?: string; // Alias for education
}

// Voter filters interface
export interface VoterFilters {
  searchTerm?: string;
  gender?: string;
  status?: string;
  polling_station?: string;
  section?: string;
  community?: string;
  religion?: string;
  economic_status?: string;
  supporter_status?: string;
  ageFrom?: number;
  ageTo?: number;
}

// Household interface
export interface Household {
  key: string;
  relationshipName: string;
  houseNumber: string;
  pollingStation: string;
  section: string;
  members: VoterData[];
  totalMembers: number;
}

// Family member interface
export interface FamilyMember {
  id: number;
  name: string;
  relationship_type?: RelationshipType;
  relationship_name?: string;
  gender: Gender;
  birth_year?: number;
  age?: number;
  house_number?: string;
  polling_station?: string;
  section?: string;
  relationshipToCurrentVoter: string;
}

// Transaction interface
export interface Transaction {
  id?: number;
  voter_id: number;
  date: string; // DD-MM-YYYY format
  purpose: string;
  amount: number; // Integer, no decimals
  created_at?: string;
}

// Column visibility interface
export interface ColumnVisibility {
  name: boolean;
  age: boolean;
  gender: boolean;
  epic: boolean;
  pollingStation: boolean;
  affiliation: boolean;
  payee: boolean;
  status: boolean;
}

// Category data interface
export interface CategoryData {
  community: string[];
  religion: string[];
  economic_status: string[];
}

// Polling station interfaces
export interface PollingStationSection {
  id: string;
  name: string;
  isSelected: boolean;
  voterCount: number;
}

export interface PollingStation {
  id: string;
  name: string;
  isOpen: boolean;
  isSelected: boolean;
  sections: PollingStationSection[];
  voterCount: number;
}

// CSV import interfaces
export interface CSVImportResult {
  success: boolean;
  totalRows: number;
  importedRows: number;
  updatedRows: number;
  skippedRows: number;
  errors: string[];
}

export interface CSVVoterRow {
  name?: string;
  relation_type?: string;
  relation_name?: string;
  house_number?: string;
  birth_year?: string | number;
  gender?: string;
  epic_number?: string;
  polling_station?: string;
  section?: string;
  phone?: string;
  email?: string;
  facebook?: string;
  instagram?: string;
  twitter?: string;
  status?: string;
  supporter_status?: string;
  education?: string;
  occupation?: string;
  community?: string;
  religion?: string;
  economic_status?: string;
  custom_notes?: string;
}

// Export interfaces
export interface ExportColumn {
  key: string;
  label: string;
  visible: boolean;
}

export interface ExportOptions {
  columns: ExportColumn[];
  data: any[];
  filename?: string;
  title?: string;
  filters?: any;
}

// Database configuration
export interface DatabaseConfig {
  wasmUrl?: string;
}

// API response interfaces
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Theme context interface
export interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

// Dialog interfaces
export interface DialogState {
  isOpen: boolean;
  title: string;
  message: string;
  variant?: 'default' | 'danger' | 'success' | 'error' | 'info';
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
}

// Settings interface
export interface AppSettings {
  theme: Theme;
  columnVisibility: ColumnVisibility;
  defaultFilters: VoterFilters;
  autoSave: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly' | 'never';
}
