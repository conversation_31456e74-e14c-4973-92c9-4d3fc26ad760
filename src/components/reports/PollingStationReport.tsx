import { useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../charts';
import { ReportingService, ReportFilters } from '../../services/ReportingService';
import { VoterData } from '../../database/VoterService';
import { formatNumber, formatPercentage, getChartColors, generateMutedColorPalette } from '../charts/ChartConfig';
import ReportFiltersComponent from './ReportFilters';
import UsersIcon from '../icons/UsersIcon';
import UserCheckIcon from '../icons/UserCheckIcon';
import HeartHandshakeIcon from '../icons/HeartHandshakeIcon';
import BuildingIcon from '../icons/BuildingIcon';

interface PollingStationReportProps {
  voters: VoterData[];
  filters: ReportFilters;
  onFiltersChange: (filters: ReportFilters) => void;
}

type SortField = 'stationName' | 'totalVoters' | 'activeVoters' | 'strongSupporters' | 'potentialSupporters' | 'undecided' | 'opposed';
type SortDirection = 'asc' | 'desc';

function PollingStationReport({ voters, filters, onFiltersChange }: PollingStationReportProps) {
  // Sorting state
  const [sortField, setSortField] = useState<SortField>('stationName');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  // Generate report data
  const reportData = useMemo(() => {
    return ReportingService.generatePollingStationReport(voters, filters);
  }, [voters, filters]);

  // Aggregate data across all stations for summary charts
  const aggregatedData = useMemo(() => {
    return reportData.reduce((acc, station) => {
      acc.totalVoters += station.totalVoters;
      acc.activeVoters += station.activeVoters;
      acc.inactiveVoters += station.inactiveVoters;
      acc.maleVoters += station.maleVoters;
      acc.femaleVoters += station.femaleVoters;
      acc.strongSupporters += station.strongSupporters;
      acc.potentialSupporters += station.potentialSupporters;
      acc.undecided += station.undecided;
      acc.opposed += station.opposed;

      // Aggregate age groups
      Object.keys(station.ageGroups).forEach(ageGroup => {
        acc.ageGroups[ageGroup] = (acc.ageGroups[ageGroup] || 0) + station.ageGroups[ageGroup as keyof typeof station.ageGroups];
      });

      // Aggregate communities
      Object.entries(station.communities).forEach(([community, count]) => {
        acc.communities[community] = (acc.communities[community] || 0) + count;
      });

      // Aggregate religions
      Object.entries(station.religions).forEach(([religion, count]) => {
        acc.religions[religion] = (acc.religions[religion] || 0) + count;
      });

      return acc;
    }, {
      totalVoters: 0,
      activeVoters: 0,
      inactiveVoters: 0,
      maleVoters: 0,
      femaleVoters: 0,
      strongSupporters: 0,
      potentialSupporters: 0,
      undecided: 0,
      opposed: 0,
      ageGroups: {} as Record<string, number>,
      communities: {} as Record<string, number>,
      religions: {} as Record<string, number>,
    });
  }, [reportData]);

  // Sort report data based on selected field and direction
  const sortedReportData = useMemo(() => {
    return [...reportData].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      if (sortField === 'stationName') {
        aValue = a.stationName;
        bValue = b.stationName;
        // Use numerical sorting for station names
        const result = aValue.localeCompare(bValue, undefined, { numeric: true, sensitivity: 'base' });
        return sortDirection === 'asc' ? result : -result;
      } else {
        // For numerical fields
        aValue = a[sortField] as number;
        bValue = b[sortField] as number;
        const result = aValue - bValue;
        return sortDirection === 'asc' ? result : -result;
      }
    });
  }, [reportData, sortField, sortDirection]);

  // Handle column header click for sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field with default direction
      setSortField(field);
      setSortDirection(field === 'stationName' ? 'asc' : 'desc'); // Default desc for numbers, asc for station names
    }
  };

  // Chart data preparations with theme-aware colors
  const chartColors = getChartColors();

  const genderData = {
    labels: chartColors.gender.labels,
    datasets: [{
      label: 'Voters by Gender',
      data: [aggregatedData.maleVoters, aggregatedData.femaleVoters],
      backgroundColor: chartColors.gender.colors,
    }],
  };

  const statusData = {
    labels: chartColors.status.labels,
    datasets: [{
      label: 'Voter Status',
      data: [aggregatedData.activeVoters, aggregatedData.inactiveVoters],
      backgroundColor: chartColors.status.colors,
    }],
  };

  const supporterData = {
    labels: chartColors.supporter.labels,
    datasets: [{
      label: 'Supporter Status',
      data: [
        aggregatedData.strongSupporters,
        aggregatedData.potentialSupporters,
        aggregatedData.undecided,
        aggregatedData.opposed,
      ],
      backgroundColor: chartColors.supporter.colors,
    }],
  };

  const ageGroupData = {
    labels: Object.keys(aggregatedData.ageGroups),
    datasets: [{
      label: 'Voters by Age Group',
      data: Object.values(aggregatedData.ageGroups),
      backgroundColor: generateMutedColorPalette(Object.keys(aggregatedData.ageGroups).length),
    }],
  };

  const stationComparisonData = {
    labels: reportData.map(station => station.stationName),
    datasets: [
      {
        label: 'Total Voters',
        data: reportData.map(station => station.totalVoters),
        backgroundColor: chartColors.primary.solid,
      },
      {
        label: 'Active Voters',
        data: reportData.map(station => station.activeVoters),
        backgroundColor: chartColors.success.solid,
      },
    ],
  };

  const topCommunitiesData = {
    labels: Object.keys(aggregatedData.communities).slice(0, 8),
    datasets: [{
      label: 'Voters by Community',
      data: Object.values(aggregatedData.communities).slice(0, 8),
      backgroundColor: generateMutedColorPalette(Object.keys(aggregatedData.communities).slice(0, 8).length),
    }],
  };

  return (
    <div className="polling-station-report">
      <div className="report-header">
        <h2>Polling Station Report</h2>
        <p>Detailed breakdown of voter demographics and political leanings by polling station</p>
      </div>

      <ReportFiltersComponent
        filters={filters}
        onFiltersChange={onFiltersChange}
        reportType="polling-station"
      />

      {/* Summary Statistics */}
      <div className="report-summary">
        <div className="summary-cards">
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Polling Stations
              </div>
              <div className="stat-icon red">
                <BuildingIcon />
              </div>
            </div>
            <div className="stat-value">{reportData.length}</div>
          </div>
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Total Voters
              </div>
              <div className="stat-icon blue">
                <UsersIcon />
              </div>
            </div>
            <div className="stat-value">{formatNumber(aggregatedData.totalVoters)}</div>
          </div>
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Active Voters
              </div>
              <div className="stat-icon green">
                <UserCheckIcon />
              </div>
            </div>
            <div className="stat-value">{formatNumber(aggregatedData.activeVoters)}</div>
            <div className="stat-change positive">
              {formatPercentage(aggregatedData.activeVoters, aggregatedData.totalVoters)}
            </div>
          </div>
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Total Supporters
              </div>
              <div className="stat-icon orange">
                <HeartHandshakeIcon />
              </div>
            </div>
            <div className="stat-value">{formatNumber(aggregatedData.strongSupporters + aggregatedData.potentialSupporters)}</div>
            <div className="stat-change positive">
              {formatPercentage(aggregatedData.strongSupporters + aggregatedData.potentialSupporters, aggregatedData.totalVoters)}
            </div>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="charts-grid">
        <div className="chart-section">
          <div className="chart-row">
            <div className="chart-item">
              <DoughnutChart
                title="Gender Distribution"
                data={genderData}
                height={300}
                centerText={{
                  value: formatNumber(aggregatedData.totalVoters),
                  label: 'Total Voters'
                }}
              />
            </div>
            <div className="chart-item">
              <PieChart
                title="Voter Status"
                data={statusData}
                height={300}
              />
            </div>
          </div>

          <div className="chart-row">
            <div className="chart-item full-width">
              <PieChart
                title="Supporter Status Distribution"
                data={supporterData}
                height={350}
              />
            </div>
          </div>

          <div className="chart-row">
            <div className="chart-item full-width">
              <BarChart
                title="Age Group Distribution"
                data={ageGroupData}
                height={300}
              />
            </div>
          </div>

          {reportData.length > 1 && (
            <div className="chart-row">
              <div className="chart-item full-width">
                <BarChart
                  title="Polling Station Comparison"
                  data={stationComparisonData}
                  height={400}
                />
              </div>
            </div>
          )}

          {Object.keys(aggregatedData.communities).length > 0 && (
            <div className="chart-row">
              <div className="chart-item full-width">
                <BarChart
                  title="Top Communities"
                  data={topCommunitiesData}
                  height={300}
                  horizontal={true}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Detailed Station Data */}
      {reportData.length > 0 && (
        <div className="station-details">
          <h3>Station-wise Details</h3>
          <div className="station-table">
            <table>
              <thead>
                <tr>
                  <th
                    onClick={() => handleSort('stationName')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Station Name"
                  >
                    Station
                    {sortField === 'stationName' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleSort('totalVoters')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Total Voters"
                  >
                    Total
                    {sortField === 'totalVoters' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleSort('activeVoters')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Active Voters"
                  >
                    Active
                    {sortField === 'activeVoters' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th>Male</th>
                  <th>Female</th>
                  <th
                    onClick={() => handleSort('strongSupporters')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Strong Supporters"
                  >
                    Strong Supporters
                    {sortField === 'strongSupporters' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleSort('potentialSupporters')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Potential Supporters"
                  >
                    Potential Supporters
                    {sortField === 'potentialSupporters' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleSort('undecided')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Undecided"
                  >
                    Undecided
                    {sortField === 'undecided' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleSort('opposed')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Opposed"
                  >
                    Opposed
                    {sortField === 'opposed' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                </tr>
              </thead>
              <tbody>
                {sortedReportData.map((station, index) => (
                  <tr key={index}>
                    <td className="station-name">{station.stationName}</td>
                    <td>{formatNumber(station.totalVoters)}</td>
                    <td>{formatNumber(station.activeVoters)}</td>
                    <td>{formatNumber(station.maleVoters)}</td>
                    <td>{formatNumber(station.femaleVoters)}</td>
                    <td>{formatNumber(station.strongSupporters)}</td>
                    <td>{formatNumber(station.potentialSupporters)}</td>
                    <td>{formatNumber(station.undecided)}</td>
                    <td>{formatNumber(station.opposed)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}

export default PollingStationReport;
