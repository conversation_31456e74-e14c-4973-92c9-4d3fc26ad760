import { useState, useEffect } from 'react';
import { ReportFilters as ReportFiltersType } from '../../services/ReportingService';
import { FilterUtils } from '../../utils/filterUtils';

interface ReportFiltersProps {
  filters: ReportFiltersType;
  onFiltersChange: (filters: ReportFiltersType) => void;
  reportType: 'polling-station' | 'household' | 'beneficiary';
}

function ReportFilters({
  filters,
  onFiltersChange,
  reportType
}: ReportFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [filterOptions, setFilterOptions] = useState({
    pollingStations: [] as string[],
    sections: [] as string[],
    communities: [] as string[],
    religions: [] as string[],
    voterStatuses: [] as string[],
    supporterStatuses: [] as string[],
  });
  const [isLoading, setIsLoading] = useState(true);

  // Load initial filter options
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        setIsLoading(true);
        const options = await FilterUtils.getReportFilterOptions();
        setFilterOptions(prevOptions => ({
          ...prevOptions,
          ...options,
        }));
      } catch (error) {
        console.error('Failed to load filter options:', error);
        // Set default empty arrays to prevent undefined errors
        setFilterOptions(prevOptions => ({
          ...prevOptions,
          pollingStations: [],
          communities: [],
          religions: [],
          voterStatuses: FilterUtils.getVoterStatusOptions() || [],
          supporterStatuses: FilterUtils.getSupporterStatusOptions() || [],
        }));
      } finally {
        setIsLoading(false);
      }
    };

    loadFilterOptions();
  }, []);

  // Load sections when polling station changes
  useEffect(() => {
    const loadSections = async () => {
      if (filters.polling_station) {
        try {
          const sections = await FilterUtils.getSectionsForPollingStation(filters.polling_station);
          setFilterOptions(prev => ({ ...prev, sections: sections || [] }));
        } catch (error) {
          console.error('Failed to load sections:', error);
          setFilterOptions(prev => ({ ...prev, sections: [] }));
        }
      } else {
        setFilterOptions(prev => ({ ...prev, sections: [] }));
      }
    };

    loadSections();
  }, [filters.polling_station]);

  const handleFilterChange = (key: keyof ReportFiltersType, value: string | number | undefined) => {
    const newFilters = { ...filters };
    if (value === '' || value === undefined) {
      delete newFilters[key];
    } else {
      (newFilters as any)[key] = value;
    }

    // Reset section when polling station changes
    if (key === 'polling_station') {
      delete newFilters.section;
    }

    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  const hasActiveFilters = Object.keys(filters).length > 0;

  return (
    <div className="report-filters">
      <div className="filter-header">
        <button
          className={`filter-toggle-btn ${isExpanded ? 'expanded' : ''}`}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M3 6h18M7 12h10m-7 6h4" />
          </svg>
          Filters
          {hasActiveFilters && <span className="filter-count">{Object.keys(filters).length}</span>}
        </button>

        {hasActiveFilters && (
          <button className="clear-filters-btn" onClick={clearFilters}>
            Clear All
          </button>
        )}
      </div>

      {isExpanded && (
        <div className="filter-content">
          {isLoading ? (
            <div style={{ padding: '20px', textAlign: 'center', color: 'var(--text-tertiary)' }}>
              Loading filter options...
            </div>
          ) : (
            <div className="filter-grid">
              {/* Common filters for all report types */}
              <div className="filter-group">
                <label>Polling Station</label>
                <div className="select-wrapper">
                  <select
                    value={filters.polling_station || ''}
                    onChange={(e) => handleFilterChange('polling_station', e.target.value)}
                  >
                    <option value="">All Stations</option>
                    {filterOptions.pollingStations?.map(station => (
                      <option key={station} value={station}>{station}</option>
                    )) || []}
                  </select>
                </div>
              </div>

              <div className="filter-group">
                <label>Section</label>
                <div className="select-wrapper">
                  <select
                    value={filters.section || ''}
                    onChange={(e) => handleFilterChange('section', e.target.value)}
                    disabled={!filters.polling_station}
                  >
                    <option value="">All Sections</option>
                    {filterOptions.sections?.map(section => (
                      <option key={section} value={section}>{section}</option>
                    )) || []}
                  </select>
                </div>
              </div>

              {/* Polling station specific filters */}
              {reportType === 'polling-station' && (
                <>
                  <div className="filter-group">
                    <label>Gender</label>
                    <div className="select-wrapper">
                      <select
                        value={filters.gender || ''}
                        onChange={(e) => handleFilterChange('gender', e.target.value)}
                      >
                        <option value="">All Genders</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>
                  </div>

                  <div className="filter-group">
                    <label>Status</label>
                    <div className="select-wrapper">
                      <select
                        value={filters.status || ''}
                        onChange={(e) => handleFilterChange('status', e.target.value)}
                      >
                        <option value="">All Statuses</option>
                        {filterOptions.voterStatuses?.map(status => (
                          <option key={status} value={status}>{status}</option>
                        )) || []}
                      </select>
                    </div>
                  </div>

                  <div className="filter-group">
                    <label>Supporter status</label>
                    <div className="select-wrapper">
                      <select
                        value={filters.supporter_status || ''}
                        onChange={(e) => handleFilterChange('supporter_status', e.target.value)}
                      >
                        <option value="">All Statuses</option>
                        {filterOptions.supporterStatuses?.map(status => (
                          <option key={status} value={status}>{status}</option>
                        )) || []}
                      </select>
                    </div>
                  </div>

                  <div className="filter-group">
                    <label>Community</label>
                    <div className="select-wrapper">
                      <select
                        value={filters.community || ''}
                        onChange={(e) => handleFilterChange('community', e.target.value)}
                      >
                        <option value="">All Communities</option>
                        {filterOptions.communities?.map(community => (
                          <option key={community} value={community}>{community}</option>
                        )) || []}
                      </select>
                    </div>
                  </div>

                  <div className="filter-group">
                    <label>Religion</label>
                    <div className="select-wrapper">
                      <select
                        value={filters.religion || ''}
                        onChange={(e) => handleFilterChange('religion', e.target.value)}
                      >
                        <option value="">All Religions</option>
                        {filterOptions.religions?.map(religion => (
                          <option key={religion} value={religion}>{religion}</option>
                        )) || []}
                      </select>
                    </div>
                  </div>

                  <div className="filter-group">
                    <label>Aage From</label>
                    <input
                      type="number"
                      min="18"
                      max="120"
                      value={filters.ageFrom || 18}
                      onChange={(e) => handleFilterChange('ageFrom', parseInt(e.target.value) || 18)}
                    />
                  </div>

                  <div className="filter-group">
                    <label>Age To</label>
                    <input
                      type="number"
                      min="18"
                      max="120"
                      value={filters.ageTo || 120}
                      onChange={(e) => handleFilterChange('ageTo', parseInt(e.target.value) || 120)}
                    />
                  </div>
                </>
              )}

              {/* Household specific filters */}
              {reportType === 'household' && (
                <>
                  <div className="filter-group">
                    <label>Min household size</label>
                    <input
                      type="number"
                      min="1"
                      value={filters.householdSizeFrom || ''}
                      onChange={(e) => handleFilterChange('householdSizeFrom', e.target.value ? parseInt(e.target.value) : undefined)}
                      placeholder="Min size"
                    />
                  </div>

                  <div className="filter-group">
                    <label>Max household size</label>
                    <input
                      type="number"
                      min="1"
                      value={filters.householdSizeTo || ''}
                      onChange={(e) => handleFilterChange('householdSizeTo', e.target.value ? parseInt(e.target.value) : undefined)}
                      placeholder="Max size"
                    />
                  </div>
                </>
              )}

              {/* Beneficiary specific filters */}
              {reportType === 'beneficiary' && (
                <>
                  <div className="filter-group">
                    <label>Date from</label>
                    <input
                      type="date"
                      value={filters.dateFrom || ''}
                      onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                    />
                  </div>

                  <div className="filter-group">
                    <label>Date to</label>
                    <input
                      type="date"
                      value={filters.dateTo || ''}
                      onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                    />
                  </div>

                  <div className="filter-group">
                    <label>Min amount</label>
                    <input
                      type="number"
                      min="0"
                      value={filters.amountFrom || ''}
                      onChange={(e) => handleFilterChange('amountFrom', e.target.value ? parseInt(e.target.value) : undefined)}
                      placeholder="Min amount"
                    />
                  </div>

                  <div className="filter-group">
                    <label>max amount</label>
                    <input
                      type="number"
                      min="0"
                      value={filters.amountTo || ''}
                      onChange={(e) => handleFilterChange('amountTo', e.target.value ? parseInt(e.target.value) : undefined)}
                      placeholder="Max amount"
                    />
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default ReportFilters;
