import { useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>hn<PERSON><PERSON><PERSON> } from '../charts';
import { ReportingService, ReportFilters } from '../../services/ReportingService';
import { Household } from '../../database/VoterService';
import { formatNumber, formatPercentage, getChartColors, generateMutedColorPalette } from '../charts/ChartConfig';
import ReportFiltersComponent from './ReportFilters';
import HomeIcon from '../icons/HomeIcon';
import UsersIcon from '../icons/UsersIcon';
import TrendingUpIcon from '../icons/TrendingUpIcon';
import HeartHandshakeIcon from '../icons/HeartHandshakeIcon';

interface HouseholdReportProps {
  households: Household[];
  filters: ReportFilters;
  onFiltersChange: (filters: ReportFilters) => void;
}

type HouseholdSortField = 'relationshipName' | 'houseNumber' | 'pollingStation' | 'memberCount' | 'supporterCount' | 'supportRate';
type SortDirection = 'asc' | 'desc';

function HouseholdReport({ households, filters, onFiltersChange }: HouseholdReportProps) {
  // Sorting state for household table
  const [sortField, setSortField] = useState<HouseholdSortField>('memberCount');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  // Generate report data
  const reportData = useMemo(() => {
    return ReportingService.generateHouseholdReport(households, filters);
  }, [households, filters]);



  // Chart data preparations with theme-aware colors
  const chartColors = getChartColors();

  const householdSizeData = {
    labels: Object.keys(reportData.householdSizeDistribution).sort((a, b) => parseInt(a) - parseInt(b)),
    datasets: [{
      label: 'Number of Households',
      data: Object.keys(reportData.householdSizeDistribution)
        .sort((a, b) => parseInt(a) - parseInt(b))
        .map(size => reportData.householdSizeDistribution[size]),
      backgroundColor: generateMutedColorPalette(Object.keys(reportData.householdSizeDistribution).length),
    }],
  };

  const supporterStatusData = {
    labels: ['All Supporters', 'Mixed Support', 'No Supporters'],
    datasets: [{
      label: 'Households by Supporter Status',
      data: [
        reportData.supporterStatusByHousehold.allSupporters,
        reportData.supporterStatusByHousehold.mixedSupport,
        reportData.supporterStatusByHousehold.noSupporters,
      ],
      backgroundColor: [
        chartColors.supporter.colors[0], // Strong Supporter
        chartColors.supporter.colors[1], // Potential Supporter
        chartColors.supporter.colors[3], // Opposed
      ],
    }],
  };

  const topHouseholdsData = {
    labels: reportData.topHouseholds.slice(0, 10).map(h => `${h.relationshipName} (${h.houseNumber})`),
    datasets: [
      {
        label: 'Total Members',
        data: reportData.topHouseholds.slice(0, 10).map(h => h.memberCount),
        backgroundColor: chartColors.primary.solid,
      },
      {
        label: 'Supporters',
        data: reportData.topHouseholds.slice(0, 10).map(h => h.supporterCount),
        backgroundColor: chartColors.success.solid,
      },
    ],
  };

  // Calculate additional metrics
  const totalMembers = Object.keys(reportData.householdSizeDistribution).reduce((total, size) => {
    return total + (parseInt(size) * reportData.householdSizeDistribution[size]);
  }, 0);

  const supporterHouseholds = reportData.supporterStatusByHousehold.allSupporters +
                             reportData.supporterStatusByHousehold.mixedSupport;

  // Sort household data based on selected field and direction
  const sortedHouseholds = useMemo(() => {
    return [...reportData.topHouseholds].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      if (sortField === 'relationshipName' || sortField === 'houseNumber' || sortField === 'pollingStation') {
        aValue = (a[sortField] || '').toString().toLowerCase();
        bValue = (b[sortField] || '').toString().toLowerCase();
        const result = aValue.localeCompare(bValue, undefined, { numeric: true, sensitivity: 'base' });
        return sortDirection === 'asc' ? result : -result;
      } else if (sortField === 'supportRate') {
        aValue = a.supporterCount / a.memberCount;
        bValue = b.supporterCount / b.memberCount;
        const result = aValue - bValue;
        return sortDirection === 'asc' ? result : -result;
      } else {
        // For memberCount and supporterCount
        aValue = a[sortField] as number;
        bValue = b[sortField] as number;
        const result = aValue - bValue;
        return sortDirection === 'asc' ? result : -result;
      }
    });
  }, [reportData.topHouseholds, sortField, sortDirection]);

  // Handle column header click for sorting
  const handleHouseholdSort = (field: HouseholdSortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field with default direction
      setSortField(field);
      setSortDirection(field === 'relationshipName' || field === 'houseNumber' || field === 'pollingStation' ? 'asc' : 'desc');
    }
  };

  return (
    <div className="household-report">
      <div className="report-header">
        <h2>Family & Household Report</h2>
        <p>Analysis of household dynamics and family-based supporter patterns</p>
      </div>

      <ReportFiltersComponent
        filters={filters}
        onFiltersChange={onFiltersChange}
        reportType="household"
      />

      {/* Summary Statistics */}
      <div className="report-summary">
        <div className="summary-cards">
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Total Households
              </div>
              <div className="stat-icon blue">
                <HomeIcon />
              </div>
            </div>
            <div className="stat-value">{formatNumber(reportData.totalHouseholds)}</div>
          </div>
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Total Members
              </div>
              <div className="stat-icon green">
                <UsersIcon />
              </div>
            </div>
            <div className="stat-value">{formatNumber(totalMembers)}</div>
          </div>
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Avg Household Size
              </div>
              <div className="stat-icon orange">
                <TrendingUpIcon />
              </div>
            </div>
            <div className="stat-value">{reportData.averageHouseholdSize}</div>
          </div>
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Households with Supporters
              </div>
              <div className="stat-icon red">
                <HeartHandshakeIcon />
              </div>
            </div>
            <div className="stat-value">{formatNumber(supporterHouseholds)}</div>
            <div className="stat-change positive">
              {formatPercentage(supporterHouseholds, reportData.totalHouseholds)}
            </div>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="charts-grid">
        <div className="chart-section">
          <div className="chart-row">
            <div className="chart-item">
              <BarChart
                title="Household Size Distribution"
                data={householdSizeData}
                height={350}
              />
            </div>
            <div className="chart-item">
              <DoughnutChart
                title="Supporter Status by Household"
                data={supporterStatusData}
                height={350}
                centerText={{
                  value: formatNumber(reportData.totalHouseholds),
                  label: 'Total Households'
                }}
              />
            </div>
          </div>

          {reportData.topHouseholds.length > 0 && (
            <div className="chart-row">
              <div className="chart-item full-width">
                <BarChart
                  title="Top 10 Largest Households"
                  data={topHouseholdsData}
                  height={400}
                  horizontal={true}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Detailed Household Data */}
      {reportData.topHouseholds.length > 0 && (
        <div className="household-details">
          <h3>Top Households by Size</h3>
          <div className="household-table">
            <table>
              <thead>
                <tr>
                  <th
                    onClick={() => handleHouseholdSort('relationshipName')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Relationship Name"
                  >
                    Relationship Name
                    {sortField === 'relationshipName' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleHouseholdSort('houseNumber')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by House Number"
                  >
                    House Number
                    {sortField === 'houseNumber' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleHouseholdSort('pollingStation')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Polling Station"
                  >
                    Polling Station
                    {sortField === 'pollingStation' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleHouseholdSort('memberCount')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Total Members"
                  >
                    Total Members
                    {sortField === 'memberCount' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleHouseholdSort('supporterCount')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Supporters"
                  >
                    Supporters
                    {sortField === 'supporterCount' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleHouseholdSort('supportRate')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Support Rate"
                  >
                    Support Rate
                    {sortField === 'supportRate' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                </tr>
              </thead>
              <tbody>
                {sortedHouseholds.slice(0, 20).map((household, index) => (
                  <tr key={index}>
                    <td className="household-name">{household.relationshipName}</td>
                    <td>{household.houseNumber}</td>
                    <td>{household.pollingStation}</td>
                    <td>{formatNumber(household.memberCount)}</td>
                    <td>{formatNumber(household.supporterCount)}</td>
                    <td className="support-rate">
                      {formatPercentage(household.supporterCount, household.memberCount)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Insights Section */}
      <div className="insights-section">
        <h3>Key Insights</h3>
        <div className="insights-grid">
          <div className="insight-card">
            <div className="insight-title">Household Penetration</div>
            <div className="insight-value">
              {formatPercentage(supporterHouseholds, reportData.totalHouseholds)}
            </div>
            <div className="insight-description">
              of households have at least one supporter
            </div>
          </div>

          <div className="insight-card">
            <div className="insight-title">Multi-Supporter Rate</div>
            <div className="insight-value">
              {formatPercentage(reportData.householdsWithMultipleSupporters, reportData.totalHouseholds)}
            </div>
            <div className="insight-description">
              of households have multiple supporters
            </div>
          </div>

          <div className="insight-card">
            <div className="insight-title">Full Support Rate</div>
            <div className="insight-value">
              {formatPercentage(reportData.supporterStatusByHousehold.allSupporters, reportData.totalHouseholds)}
            </div>
            <div className="insight-description">
              of households are fully supportive
            </div>
          </div>

          <div className="insight-card">
            <div className="insight-title">Average Size</div>
            <div className="insight-value">
              {reportData.averageHouseholdSize}
            </div>
            <div className="insight-description">
              members per household
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default HouseholdReport;
