import { useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../charts';
import { ReportingService, ReportFilters } from '../../services/ReportingService';
import { Transaction } from '../../database/TransactionService';
import { VoterData } from '../../database/VoterService';
import { formatNumber, formatCurrency, formatPercentage, getChartColors, generateMutedColorPalette } from '../charts/ChartConfig';
import ReportFiltersComponent from './ReportFilters';
import DollarSignIcon from '../icons/DollarSignIcon';
import TrendingUpIcon from '../icons/TrendingUpIcon';
import UsersIcon from '../icons/UsersIcon';
import HeartHandshakeIcon from '../icons/HeartHandshakeIcon';

interface BeneficiaryReportProps {
  transactions: Transaction[];
  voters: VoterData[];
  filters: ReportFilters;
  onFiltersChange: (filters: ReportFilters) => void;
}

type BeneficiarySortField = 'voterName' | 'totalAmount' | 'transactionCount' | 'averageAmount' | 'supporterStatus';
type SortDirection = 'asc' | 'desc';

function BeneficiaryReport({ transactions, voters, filters, onFiltersChange }: BeneficiaryReportProps) {
  // Sorting state for beneficiary table
  const [sortField, setSortField] = useState<BeneficiarySortField>('totalAmount');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Generate report data
  const reportData = useMemo(() => {
    return ReportingService.generateBeneficiaryReport(transactions, voters, filters);
  }, [transactions, voters, filters]);

  // Chart data preparations with theme-aware colors
  const chartColors = getChartColors();

  const monthlyTrendData = {
    labels: Object.keys(reportData.amountByMonth).sort(),
    datasets: [{
      label: 'Monthly Distribution Amount',
      data: Object.keys(reportData.amountByMonth)
        .sort()
        .map(month => reportData.amountByMonth[month]),
      borderColor: chartColors.primary.solid,
      backgroundColor: chartColors.primary.transparent,
      fill: true,
    }],
  };

  const supporterStatusData = {
    labels: Object.keys(reportData.amountBySupporterStatus),
    datasets: [{
      label: 'Amount by Supporter Status',
      data: Object.values(reportData.amountBySupporterStatus),
      backgroundColor: generateMutedColorPalette(Object.keys(reportData.amountBySupporterStatus).length),
    }],
  };

  const pollingStationData = {
    labels: Object.keys(reportData.amountByPollingStation).slice(0, 10),
    datasets: [{
      label: 'Amount Distributed',
      data: Object.values(reportData.amountByPollingStation).slice(0, 10),
      backgroundColor: generateMutedColorPalette(Object.keys(reportData.amountByPollingStation).slice(0, 10).length),
    }],
  };

  const topBeneficiariesData = {
    labels: reportData.topBeneficiaries.slice(0, 10).map(b => b.voterName),
    datasets: [{
      label: 'Total Amount Received',
      data: reportData.topBeneficiaries.slice(0, 10).map(b => b.totalAmount),
      backgroundColor: chartColors.success.solid,
    }],
  };

  const transactionPurposesData = {
    labels: Object.keys(reportData.transactionPurposes),
    datasets: [{
      label: 'Number of Transactions',
      data: Object.values(reportData.transactionPurposes),
      backgroundColor: generateMutedColorPalette(Object.keys(reportData.transactionPurposes).length),
    }],
  };

  // Calculate additional metrics
  const uniqueBeneficiaries = reportData.topBeneficiaries.length;

  // Sort beneficiary data based on selected field and direction
  const sortedBeneficiaries = useMemo(() => {
    return [...reportData.topBeneficiaries].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      if (sortField === 'voterName' || sortField === 'supporterStatus') {
        aValue = (sortField === 'voterName' ? a.voterName : (a.supporterStatus || 'Unknown')).toLowerCase();
        bValue = (sortField === 'voterName' ? b.voterName : (b.supporterStatus || 'Unknown')).toLowerCase();
        const result = aValue.localeCompare(bValue);
        return sortDirection === 'asc' ? result : -result;
      } else if (sortField === 'averageAmount') {
        aValue = a.totalAmount / a.transactionCount;
        bValue = b.totalAmount / b.transactionCount;
        const result = aValue - bValue;
        return sortDirection === 'asc' ? result : -result;
      } else {
        // For totalAmount and transactionCount
        aValue = a[sortField] as number;
        bValue = b[sortField] as number;
        const result = aValue - bValue;
        return sortDirection === 'asc' ? result : -result;
      }
    });
  }, [reportData.topBeneficiaries, sortField, sortDirection]);

  // Handle column header click for sorting
  const handleBeneficiarySort = (field: BeneficiarySortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field with default direction
      setSortField(field);
      setSortDirection(field === 'voterName' || field === 'supporterStatus' ? 'asc' : 'desc');
    }
  };

  return (
    <div className="beneficiary-report">
      <div className="report-header">
        <h2>Beneficiary Report</h2>
        <p>Analysis of financial assistance distribution and beneficiary patterns</p>
      </div>

      <ReportFiltersComponent
        filters={filters}
        onFiltersChange={onFiltersChange}
        reportType="beneficiary"
      />

      {/* Summary Statistics */}
      <div className="report-summary">
        <div className="summary-cards">
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Total Amount Distributed
              </div>
              <div className="stat-icon blue">
                <DollarSignIcon />
              </div>
            </div>
            <div className="stat-value">{formatCurrency(reportData.totalAmount)}</div>
          </div>
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Total Transactions
              </div>
              <div className="stat-icon green">
                <TrendingUpIcon />
              </div>
            </div>
            <div className="stat-value">{formatNumber(reportData.totalTransactions)}</div>
          </div>
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Unique Beneficiaries
              </div>
              <div className="stat-icon orange">
                <UsersIcon />
              </div>
            </div>
            <div className="stat-value">{formatNumber(uniqueBeneficiaries)}</div>
          </div>
          <div className="summary-card">
            <div className="stat-header">
              <div className="stat-label">
                Average per Transaction
              </div>
              <div className="stat-icon red">
                <HeartHandshakeIcon />
              </div>
            </div>
            <div className="stat-value">{formatCurrency(reportData.averageAmount)}</div>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="charts-grid">
        <div className="chart-section">
          {Object.keys(reportData.amountByMonth).length > 1 && (
            <div className="chart-row">
              <div className="chart-item full-width">
                <LineChart
                  title="Monthly Distribution Trend"
                  data={monthlyTrendData}
                  height={300}
                  area={true}
                />
              </div>
            </div>
          )}

          <div className="chart-row">
            <div className="chart-item">
              <DoughnutChart
                title="Distribution by Supporter Status"
                data={supporterStatusData}
                height={350}
                centerText={{
                  value: formatCurrency(reportData.totalAmount),
                  label: 'Total Amount'
                }}
              />
            </div>
            <div className="chart-item">
              <PieChart
                title="Transactions by Purpose"
                data={transactionPurposesData}
                height={350}
              />
            </div>
          </div>

          {Object.keys(reportData.amountByPollingStation).length > 1 && (
            <div className="chart-row">
              <div className="chart-item full-width">
                <BarChart
                  title="Top Polling Stations by Amount"
                  data={pollingStationData}
                  height={300}
                  horizontal={true}
                />
              </div>
            </div>
          )}

          {reportData.topBeneficiaries.length > 0 && (
            <div className="chart-row">
              <div className="chart-item full-width">
                <BarChart
                  title="Top 10 Beneficiaries"
                  data={topBeneficiariesData}
                  height={400}
                  horizontal={true}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Detailed Beneficiary Data */}
      {reportData.topBeneficiaries.length > 0 && (
        <div className="beneficiary-details">
          <h3>Top Beneficiaries</h3>
          <div className="beneficiary-table">
            <table>
              <thead>
                <tr>
                  <th
                    onClick={() => handleBeneficiarySort('voterName')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Beneficiary Name"
                  >
                    Beneficiary Name
                    {sortField === 'voterName' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleBeneficiarySort('totalAmount')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Total Amount"
                  >
                    Total Amount
                    {sortField === 'totalAmount' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleBeneficiarySort('transactionCount')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Transactions"
                  >
                    Transactions
                    {sortField === 'transactionCount' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleBeneficiarySort('averageAmount')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Average Amount"
                  >
                    Average Amount
                    {sortField === 'averageAmount' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    onClick={() => handleBeneficiarySort('supporterStatus')}
                    style={{ cursor: 'pointer', userSelect: 'none' }}
                    title="Click to sort by Supporter Status"
                  >
                    Supporter Status
                    {sortField === 'supporterStatus' && (
                      <span style={{ marginLeft: '4px' }}>
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                </tr>
              </thead>
              <tbody>
                {sortedBeneficiaries.slice(0, 20).map((beneficiary, index) => (
                  <tr key={index}>
                    <td className="beneficiary-name">{beneficiary.voterName}</td>
                    <td className="amount">{formatCurrency(beneficiary.totalAmount)}</td>
                    <td>{formatNumber(beneficiary.transactionCount)}</td>
                    <td className="amount">
                      {formatCurrency(Math.round(beneficiary.totalAmount / beneficiary.transactionCount))}
                    </td>
                    <td className={`supporter-status ${beneficiary.supporterStatus?.toLowerCase().replace(' ', '-')}`}>
                      {beneficiary.supporterStatus || 'Unknown'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Insights Section */}
      <div className="insights-section">
        <h3>Key Insights</h3>
        <div className="insights-grid">
          <div className="insight-card">
            <div className="insight-title">Distribution Efficiency</div>
            <div className="insight-value">
              {formatPercentage(uniqueBeneficiaries, voters.length)}
            </div>
            <div className="insight-description">
              of total voters received assistance
            </div>
          </div>

          <div className="insight-card">
            <div className="insight-title">Supporter Correlation</div>
            <div className="insight-value">
              {formatCurrency(reportData.amountBySupporterStatus['Strong Supporter'] || 0)}
            </div>
            <div className="insight-description">
              distributed to strong supporters
            </div>
          </div>

          <div className="insight-card">
            <div className="insight-title">Transaction Frequency</div>
            <div className="insight-value">
              {uniqueBeneficiaries > 0 ? (reportData.totalTransactions / uniqueBeneficiaries).toFixed(1) : '0'}
            </div>
            <div className="insight-description">
              average transactions per beneficiary
            </div>
          </div>

          <div className="insight-card">
            <div className="insight-title">Top Purpose</div>
            <div className="insight-value">
              {Object.keys(reportData.transactionPurposes).length > 0
                ? Object.keys(reportData.transactionPurposes).reduce((a, b) =>
                  reportData.transactionPurposes[a] > reportData.transactionPurposes[b] ? a : b
                )
                : 'N/A'
              }
            </div>
            <div className="insight-description">
              most common transaction purpose
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BeneficiaryReport;
