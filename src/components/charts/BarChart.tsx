
import { Bar } from 'react-chartjs-2';
import { ChartData, ChartOptions } from 'chart.js';
import { useTheme } from '../../context/ThemeContext';
import { getBarChartOptions, generateColorPalette, generateTransparentColors } from './ChartConfig';

interface BarChartProps {
  title?: string;
  data: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      backgroundColor?: string | string[];
      borderColor?: string | string[];
      borderWidth?: number;
    }>;
  };
  options?: Partial<ChartOptions<'bar'>>;
  height?: number;
  horizontal?: boolean;
}

function BarChart({ title, data, options = {}, height = 300, horizontal = false }: BarChartProps) {
  const { isDarkTheme } = useTheme();

  // Generate colors if not provided
  const processedData: ChartData<'bar'> = {
    ...data,
    datasets: data.datasets.map((dataset) => {
      const colors = generateColorPalette(dataset.data.length);
      const transparentColors = generateTransparentColors(colors, 0.8);

      return {
        ...dataset,
        backgroundColor: dataset.backgroundColor || transparentColors,
        borderColor: dataset.borderColor || colors,
        borderWidth: dataset.borderWidth || 1,
      };
    }),
  };

  // Merge options with defaults
  const chartOptions: ChartOptions<'bar'> = {
    ...getBarChartOptions(isDarkTheme),
    ...options,
    indexAxis: horizontal ? 'y' : 'x',
    plugins: {
      ...getBarChartOptions(isDarkTheme).plugins,
      ...options.plugins,
      title: title ? {
        display: true,
        text: title,
        color: isDarkTheme ? '#f4f4f5' : '#1f1f1f',
        font: {
          family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          size: 16,
          weight: 600,
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      } : undefined,
    },
  };

  return (
    <div className="chart-container" style={{ height: `${height}px` }}>
      <Bar data={processedData} options={chartOptions} />
    </div>
  );
}

export default BarChart;
