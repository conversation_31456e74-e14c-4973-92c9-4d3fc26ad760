
import { Line } from 'react-chartjs-2';
import { ChartData, ChartOptions } from 'chart.js';
import { useTheme } from '../../context/ThemeContext';
import { getLineChartOptions, generateColorPalette, generateTransparentColors } from './ChartConfig';

interface LineChartProps {
  title?: string;
  data: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      backgroundColor?: string | string[];
      borderColor?: string | string[];
      borderWidth?: number;
      fill?: boolean;
      tension?: number;
    }>;
  };
  options?: Partial<ChartOptions<'line'>>;
  height?: number;
  area?: boolean; // Whether to fill the area under the line
}

function LineChart({ title, data, options = {}, height = 300, area = false }: LineChartProps) {
  const { isDarkTheme } = useTheme();

  // Generate colors if not provided
  const processedData: ChartData<'line'> = {
    ...data,
    datasets: data.datasets.map((dataset, index) => {
      const colors = generateColorPalette(data.datasets.length);
      const transparentColors = generateTransparentColors(colors, 0.2);

      return {
        ...dataset,
        backgroundColor: area ? (dataset.backgroundColor || transparentColors[index]) : 'transparent',
        borderColor: dataset.borderColor || colors[index],
        borderWidth: dataset.borderWidth || 2,
        fill: dataset.fill !== undefined ? dataset.fill : area,
        tension: dataset.tension !== undefined ? dataset.tension : 0.3,
        pointBackgroundColor: dataset.borderColor || colors[index],
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
      };
    }),
  };

  // Merge options with defaults
  const chartOptions: ChartOptions<'line'> = {
    ...getLineChartOptions(isDarkTheme),
    ...options,
    plugins: {
      ...getLineChartOptions(isDarkTheme).plugins,
      ...options.plugins,
      title: title ? {
        display: true,
        text: title,
        color: isDarkTheme ? '#f4f4f5' : '#1f1f1f',
        font: {
          family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          size: 16,
          weight: 600,
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      } : undefined,
    },
  };

  return (
    <div className="chart-container" style={{ height: `${height}px` }}>
      <Line data={processedData} options={chartOptions} />
    </div>
  );
}

export default LineChart;
