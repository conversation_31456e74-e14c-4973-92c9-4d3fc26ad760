import { Pie } from 'react-chartjs-2';
import { ChartData, ChartOptions } from 'chart.js';
import { useTheme } from '../../context/ThemeContext';
import { getPieChartOptions, generateColorPalette, generateTransparentColors } from './ChartConfig';

interface PieChartProps {
  title?: string;
  data: {
    labels: string[];
    datasets: Array<{
      label?: string;
      data: number[];
      backgroundColor?: string | string[];
      borderColor?: string | string[];
      borderWidth?: number;
    }>;
  };
  options?: Partial<ChartOptions<'pie'>>;
  height?: number;
}

function PieChart({ title, data, options = {}, height = 300 }: PieChartProps) {
  const { isDarkTheme } = useTheme();

  // Generate colors if not provided
  const processedData: ChartData<'pie'> = {
    ...data,
    datasets: data.datasets.map((dataset) => {
      const colors = generateColorPalette(data.labels.length);
      const transparentColors = generateTransparentColors(colors, 0.9);

      return {
        ...dataset,
        backgroundColor: dataset.backgroundColor || transparentColors,
        borderColor: dataset.borderColor || colors,
        borderWidth: dataset.borderWidth || 2,
      };
    }),
  };

  // Merge options with defaults
  const chartOptions: ChartOptions<'pie'> = {
    ...getPieChartOptions(isDarkTheme),
    ...options,
    plugins: {
      ...getPieChartOptions(isDarkTheme).plugins,
      ...options.plugins,
      title: title ? {
        display: true,
        text: title,
        color: isDarkTheme ? '#f4f4f5' : '#1f1f1f',
        font: {
          family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          size: 16,
          weight: 600,
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      } : undefined,
    },
  };

  return (
    <div className="chart-container" style={{ height: `${height}px` }}>
      <Pie data={processedData} options={chartOptions} />
    </div>
  );
}

export default PieChart;
