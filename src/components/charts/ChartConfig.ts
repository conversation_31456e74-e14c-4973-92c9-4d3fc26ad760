import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ChartOptions,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// Function to get CSS variable value
const getCSSVariable = (variable: string): string => {
  if (typeof window !== 'undefined') {
    return getComputedStyle(document.documentElement).getPropertyValue(variable).trim();
  }
  return '';
};

// Function to convert CSS variable to hex color
const getCSSColor = (variable: string, fallback: string): string => {
  const value = getCSSVariable(variable);
  return value || fallback;
};

// Dynamic color palette that reads from CSS variables (using muted icon colors)
export const getThemeColors = () => ({
  // Use the muted icon colors instead of bright primary colors
  primary: getCSSColor('--text-icon-blue', '#93c5fd'),
  success: getCSSColor('--text-icon-green', '#d8b4fe'),
  danger: getCSSColor('--text-icon-red', '#fca5a5'),
  warning: getCSSColor('--text-icon-orange', '#fdba74'),
  info: getCSSColor('--text-icon-green', '#d8b4fe'),

  // Gender colors (using muted versions)
  male: getCSSColor('--text-icon-blue', '#93c5fd'),
  female: getCSSColor('--text-icon-green', '#d8b4fe'),

  // Status colors (using muted versions)
  active: getCSSColor('--text-icon-green', '#d8b4fe'),
  inactive: getCSSColor('--text-icon-red', '#fca5a5'),

  // Supporter status colors (using muted versions)
  strongSupporter: getCSSColor('--text-icon-green', '#d8b4fe'),
  potentialSupporter: getCSSColor('--text-icon-orange', '#fdba74'),
  undecided: getCSSColor('--text-icon-blue', '#93c5fd'),
  opposed: getCSSColor('--text-icon-red', '#fca5a5'),

  // Icon colors (muted versions)
  blue: getCSSColor('--text-icon-blue', '#93c5fd'),
  green: getCSSColor('--text-icon-green', '#d8b4fe'),
  orange: getCSSColor('--text-icon-orange', '#fdba74'),
  red: getCSSColor('--text-icon-red', '#fca5a5'),
});

// Static fallback colors for server-side rendering or when CSS variables aren't available
export const CHART_COLORS = {
  primary: '#1a73e8',
  success: '#34c759',
  danger: '#ff3b30',
  warning: '#ff9500',
  info: '#8a4dff',

  // Gender colors
  male: '#1a73e8',
  female: '#8a4dff',

  // Status colors
  active: '#34c759',
  inactive: '#ff3b30',

  // Supporter status colors
  strongSupporter: '#34c759',
  potentialSupporter: '#ff9500',
  undecided: '#8a4dff',
  opposed: '#ff3b30',

  // Additional chart colors for variety
  blue: '#1a73e8',
  green: '#34c759',
  orange: '#ff9500',
  red: '#ff3b30',
  purple: '#8a4dff',
  teal: '#00bcd4',
  pink: '#e91e63',
  indigo: '#3f51b5',
  cyan: '#00acc1',
  amber: '#ffc107',
};

// Generate color palette for multiple data series using muted theme colors
export const generateColorPalette = (count: number): string[] => {
  const themeColors = getThemeColors();
  const baseColors = [
    themeColors.blue,      // Muted blue
    themeColors.green,     // Muted purple/green
    themeColors.orange,    // Muted orange
    themeColors.red,       // Muted red
    themeColors.primary,   // Muted primary
    themeColors.success,   // Muted success
    themeColors.warning,   // Muted warning
    themeColors.danger,    // Muted danger
  ];
  const colors: string[] = [];

  for (let i = 0; i < count; i++) {
    colors.push(baseColors[i % baseColors.length]);
  }

  return colors;
};

// Generate transparent colors for backgrounds
export const generateTransparentColors = (colors: string[], alpha: number = 0.2): string[] => {
  return colors.map(color => {
    // Convert hex to rgba
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  });
};

// Generate extended muted color palette for charts with many data points
export const generateMutedColorPalette = (count: number): string[] => {
  // Additional muted colors that work well together
  const mutedColors = [
    getCSSColor('--text-icon-blue', '#93c5fd'),    // Muted blue
    getCSSColor('--text-icon-green', '#d8b4fe'),   // Muted purple
    getCSSColor('--text-icon-orange', '#fdba74'),  // Muted orange
    getCSSColor('--text-icon-red', '#fca5a5'),     // Muted red
    '#a78bfa',  // Muted violet
    '#60a5fa',  // Muted sky blue
    '#34d399',  // Muted emerald
    '#fbbf24',  // Muted amber
    '#f87171',  // Muted rose
    '#a3a3a3',  // Muted gray
    '#8b5cf6',  // Muted purple
    '#06b6d4',  // Muted cyan
  ];

  const colors: string[] = [];
  for (let i = 0; i < count; i++) {
    colors.push(mutedColors[i % mutedColors.length]);
  }

  return colors;
};

// Get theme-aware colors for specific chart types
export const getChartColors = () => {
  const themeColors = getThemeColors();

  return {
    // Gender colors
    gender: {
      labels: ['Male', 'Female'],
      colors: [themeColors.male, themeColors.female],
    },

    // Status colors
    status: {
      labels: ['Active', 'Inactive'],
      colors: [themeColors.active, themeColors.inactive],
    },

    // Supporter status colors
    supporter: {
      labels: ['Strong Supporter', 'Potential Supporter', 'Undecided', 'Opposed'],
      colors: [
        themeColors.strongSupporter,
        themeColors.potentialSupporter,
        themeColors.undecided,
        themeColors.opposed,
      ],
    },

    // Primary color variations for single-series charts
    primary: {
      solid: themeColors.primary,
      transparent: generateTransparentColors([themeColors.primary], 0.3)[0],
      gradient: generateTransparentColors([themeColors.primary], 0.15)[0],
    },

    // Success color variations
    success: {
      solid: themeColors.success,
      transparent: generateTransparentColors([themeColors.success], 0.3)[0],
      gradient: generateTransparentColors([themeColors.success], 0.15)[0],
    },
  };
};

// Common chart options that match the application's design
export const getBaseChartOptions = (isDarkTheme: boolean = false): Partial<ChartOptions> => {
  // Use CSS variables for dynamic theming
  const textColor = getCSSColor('--text-primary', isDarkTheme ? '#f4f4f5' : '#1f1f1f');
  const textSecondary = getCSSColor('--text-secondary', isDarkTheme ? '#e4e4e7' : '#333333');
  const textTertiary = getCSSColor('--text-tertiary', isDarkTheme ? 'rgba(161, 161, 170, 0.85)' : 'rgba(100, 100, 110, 0.85)');
  const borderColor = getCSSColor('--border-primary', isDarkTheme ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.15)');
  const cardBackground = getCSSColor('--bg-card', isDarkTheme ? 'rgba(25, 25, 25, 0.25)' : 'rgba(255, 255, 255, 0.2)');

  return {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: textSecondary,
          font: {
            family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            size: 12,
            weight: 500,
          },
          padding: 20,
          usePointStyle: true,
        },
      },
      tooltip: {
        backgroundColor: cardBackground,
        titleColor: textColor,
        bodyColor: textSecondary,
        borderColor: borderColor,
        borderWidth: 1,
        cornerRadius: 12,
        padding: 12,
        titleFont: {
          family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          size: 13,
          weight: 600,
        },
        bodyFont: {
          family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          size: 12,
        },
      },
    },
    scales: {
      x: {
        grid: {
          color: borderColor,
          display: true,
        },
        ticks: {
          color: textTertiary,
          font: {
            family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            size: 11,
          },
        },
      },
      y: {
        grid: {
          color: borderColor,
          display: true,
        },
        ticks: {
          color: textTertiary,
          font: {
            family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            size: 11,
          },
        },
      },
    },
    elements: {
      bar: {
        borderRadius: 6,
        borderSkipped: false,
      },
      line: {
        tension: 0.3,
        borderWidth: 2,
      },
      point: {
        radius: 4,
        hoverRadius: 6,
        borderWidth: 2,
      },
    },
    animation: {
      duration: 750,
      easing: 'easeInOutQuart',
    },
  };
};

// Specific options for bar charts
export const getBarChartOptions = (isDarkTheme: boolean = false): ChartOptions<'bar'> => {
  const baseOptions = getBaseChartOptions(isDarkTheme);

  return {
    ...baseOptions,
    scales: {
      ...baseOptions.scales,
      y: {
        ...baseOptions.scales?.y,
        beginAtZero: true,
      },
    },
  } as ChartOptions<'bar'>;
};

// Specific options for line charts
export const getLineChartOptions = (isDarkTheme: boolean = false): ChartOptions<'line'> => {
  const baseOptions = getBaseChartOptions(isDarkTheme);

  return {
    ...baseOptions,
    scales: {
      ...baseOptions.scales,
      y: {
        ...baseOptions.scales?.y,
        beginAtZero: true,
      },
    },
  } as ChartOptions<'line'>;
};

// Specific options for pie/doughnut charts
export const getPieChartOptions = (isDarkTheme: boolean = false): ChartOptions<'pie'> => {
  const baseOptions = getBaseChartOptions(isDarkTheme);

  return {
    ...baseOptions,
    scales: undefined, // Pie charts don't use scales
    plugins: {
      ...baseOptions.plugins,
      legend: {
        ...baseOptions.plugins?.legend,
        position: 'right' as const,
      },
    },
  } as ChartOptions<'pie'>;
};

// Specific options for doughnut charts
export const getDoughnutChartOptions = (isDarkTheme: boolean = false): ChartOptions<'doughnut'> => {
  const baseOptions = getBaseChartOptions(isDarkTheme);

  return {
    ...baseOptions,
    scales: undefined, // Doughnut charts don't use scales
    plugins: {
      ...baseOptions.plugins,
      legend: {
        ...baseOptions.plugins?.legend,
        position: 'right' as const,
      },
    },
    cutout: '60%', // Creates the doughnut hole
  } as ChartOptions<'doughnut'>;
};

// Format numbers for display
export const formatNumber = (value: number): string => {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M';
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K';
  }
  return value.toString();
};

// Format currency for display
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

// Format percentage for display
export const formatPercentage = (value: number, total: number): string => {
  if (total === 0) return '0%';
  return ((value / total) * 100).toFixed(1) + '%';
};
