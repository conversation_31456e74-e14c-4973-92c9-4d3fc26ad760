
import { Doughnut } from 'react-chartjs-2';
import { ChartData, ChartOptions } from 'chart.js';
import { useTheme } from '../../context/ThemeContext';
import { getDoughnutChartOptions, generateColorPalette, generateTransparentColors } from './ChartConfig';

interface DoughnutChartProps {
  title?: string;
  data: {
    labels: string[];
    datasets: Array<{
      label?: string;
      data: number[];
      backgroundColor?: string | string[];
      borderColor?: string | string[];
      borderWidth?: number;
    }>;
  };
  options?: Partial<ChartOptions<'doughnut'>>;
  height?: number;
  centerText?: {
    value: string | number;
    label: string;
  };
}

function DoughnutChart({ title, data, options = {}, height = 300, centerText }: DoughnutChartProps) {
  const { isDarkTheme } = useTheme();

  // Generate colors if not provided
  const processedData: ChartData<'doughnut'> = {
    ...data,
    datasets: data.datasets.map((dataset) => {
      const colors = generateColorPalette(data.labels.length);
      const transparentColors = generateTransparentColors(colors, 0.9);

      return {
        ...dataset,
        backgroundColor: dataset.backgroundColor || transparentColors,
        borderColor: dataset.borderColor || colors,
        borderWidth: dataset.borderWidth || 2,
      };
    }),
  };

  // Merge options with defaults
  const chartOptions: ChartOptions<'doughnut'> = {
    ...getDoughnutChartOptions(isDarkTheme),
    ...options,
    plugins: {
      ...getDoughnutChartOptions(isDarkTheme).plugins,
      ...options.plugins,
      title: title ? {
        display: true,
        text: title,
        color: isDarkTheme ? '#f4f4f5' : '#1f1f1f',
        font: {
          family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          size: 16,
          weight: 600,
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      } : undefined,
    },
  };

  return (
    <div className="chart-container" style={{ height: `${height}px`, position: 'relative' }}>
      <Doughnut data={processedData} options={chartOptions} />
      {centerText && (
        <div
          className="doughnut-center-text"
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            pointerEvents: 'none',
            color: isDarkTheme ? '#f4f4f5' : '#1f1f1f',
          }}
        >
          <div style={{
            fontSize: '24px',
            fontWeight: '600',
            lineHeight: '1.2',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          }}>
            {centerText.value}
          </div>
          <div style={{
            fontSize: '12px',
            opacity: 0.7,
            marginTop: '4px',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          }}>
            {centerText.label}
          </div>
        </div>
      )}
    </div>
  );
}

export default DoughnutChart;
