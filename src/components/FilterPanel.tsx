import { useRef, useEffect } from 'react';
import { useApp } from '../context/AppContext';
import { useLightSweep } from '../hooks/useLightSweep';

function FilterPanel() {
  const { state, dispatch } = useApp();
  const { filters, categoryData, isFilterPanelOpen } = state;
  const containerRef = useRef<HTMLDivElement>(null);
  const triggerLightSweep = useLightSweep();

  const handleFilterChange = (key: keyof typeof filters, value: any) => {
    dispatch({ type: 'UPDATE_FILTER', payload: { key, value } });
  };

  const handleClearFilters = () => {
    dispatch({ type: 'CLEAR_FILTERS' });
    triggerLightSweep(containerRef.current);
  };

  // Trigger light sweep when panel opens
  useEffect(() => {
    if (isFilterPanelOpen) {
      triggerLightSweep(containerRef.current);
    }
  }, [isFilterPanelOpen, triggerLightSweep]);

  return (
    <div
      ref={containerRef}
      className={`filter-options-container ${isFilterPanelOpen ? 'is-open' : ''}`}
    >
      <div className="filter-options">
        <div className="filter-header">
          <div className="filter-title">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
            </svg>
            Filter Options
          </div>
          <button
            onClick={handleClearFilters}
            className="clear-filters"
            aria-label="Clear all filters"
          >
            Clear Filters
          </button>
        </div>
        <div className="filter-controls">
          <div className="filter-section">
            <div className="filter-group">
              <label htmlFor="gender-select">Gender</label>
              <div className="select-wrapper">
                <select
                  id="gender-select"
                  value={filters.gender}
                  onChange={e => handleFilterChange('gender', e.target.value)}
                >
                  <option>All</option>
                  <option>Male</option>
                  <option>Female</option>
                </select>
              </div>
            </div>
          </div>
          <div className="filter-divider"></div>
          <div className="filter-section">
            <div className="filter-group">
              <label htmlFor="age-from">Age From</label>
              <input
                id="age-from"
                type="number"
                value={filters.ageFrom}
                onChange={e => handleFilterChange('ageFrom', parseInt(e.target.value) || 18)}
                min="18"
                max="120"
              />
            </div>
            <div className="filter-group">
              <label htmlFor="age-to">Age To</label>
              <input
                id="age-to"
                type="number"
                value={filters.ageTo}
                onChange={e => handleFilterChange('ageTo', parseInt(e.target.value) || 120)}
                min="18"
                max="120"
              />
            </div>
          </div>
          <div className="filter-divider"></div>
          <div className="filter-section">
            <div className="filter-group">
              <label htmlFor="community-select">Community</label>
              <div className="select-wrapper">
                <select
                  id="community-select"
                  value={filters.community}
                  onChange={e => handleFilterChange('community', e.target.value)}
                >
                  <option>All</option>
                  {categoryData.community.map(item => (
                    <option key={item} value={item}>
                      {item}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="filter-group">
              <label htmlFor="religion-select">Religion</label>
              <div className="select-wrapper">
                <select
                  id="religion-select"
                  value={filters.religion}
                  onChange={e => handleFilterChange('religion', e.target.value)}
                >
                  <option>All</option>
                  {categoryData.religion.map(item => (
                    <option key={item} value={item}>
                      {item}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="filter-group">
              <label htmlFor="economic-select">Economic Status</label>
              <div className="select-wrapper">
                <select
                  id="economic-select"
                  value={filters.economicStatus}
                  onChange={e => handleFilterChange('economicStatus', e.target.value)}
                >
                  <option>All</option>
                  {categoryData.economic_status.map(item => (
                    <option key={item} value={item}>
                      {item}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FilterPanel;
