import React from 'react';
import PollingStationNode from './PollingStationNode';

interface Section {
  id: string;
  name: string;
  isSelected: boolean;
  voterCount: number;
}

interface PollingStation {
  id: string;
  name: string;
  isOpen: boolean;
  isSelected: boolean;
  sections: Section[];
  voterCount: number;
}

interface PollingStationListProps {
  pollingStations: PollingStation[];
  onToggle: (e: React.MouseEvent, stationId: string) => void;
  onStationSelect: (stationId: string) => void;
  onSectionSelect: (stationId: string, sectionId: string) => void;
}

const PollingStationList: React.FC<PollingStationListProps> = ({
  pollingStations,
  onToggle,
  onStationSelect,
  onSectionSelect,
}) => {
  return (
    <section className="sidebar-section">
      <ul className="tree">
        {pollingStations.map(station => (
          <PollingStationNode
            key={station.id}
            station={station}
            onToggle={onToggle}
            onStationSelect={onStationSelect}
            onSectionSelect={onSectionSelect}
          />
        ))}
      </ul>
    </section>
  );
};

export default PollingStationList;
