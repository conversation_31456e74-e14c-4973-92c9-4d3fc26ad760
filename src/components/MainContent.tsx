
import MainView from './MainView';
import { useApp } from '../context/AppContext';

function MainContent() {
  const { state, dispatch } = useApp();

  const handleFilterToggle = () => {
    dispatch({ type: 'TOGGLE_FILTER_PANEL' });
  };

  const handleAddVoter = () => {
    // Create an empty voter object for new voter creation
    const emptyVoter = {
      id: 0, // Temporary ID for new voter
      name: '',
      relationship_type: 'Father' as const,
      relationship_name: '',
      gender: 'Male' as const,
      birth_year: undefined,
      epic_number: '',
      house_number: '',
      polling_station: '',
      section: '',
      phone: '',
      email: '',
      facebook: '',
      instagram: '',
      twitter: '',
      status: 'Active' as const,
      supporter_status: undefined,
      education: '',
      occupation: '',
      community: '',
      religion: '',
      economic_status: '',
      custom_notes: '',
      // Computed fields for compatibility
      age: undefined,
      epic: '',
      pollingStation: '',
      economicStatus: '',
      fatherName: '',
      qualification: '',
    };

    // Open voter panel with empty voter for creation
    dispatch({ type: 'TOGGLE_VOTER_PANEL', payload: emptyVoter });
  };



  return (
    <main className="main-content">
      <MainView
        onFilterToggle={handleFilterToggle}
        onAddVoter={handleAddVoter}
        isFilterPanelOpen={state.isFilterPanelOpen}
        importProgress={state.importProgress}
      />
    </main>
  );
}

export default MainContent;
