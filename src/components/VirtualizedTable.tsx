import React, { useState, useRef, useMemo } from 'react';

interface VirtualizedTableProps<T> {
  data: T[];
  columns: Array<{
    key: string;
    label: string;
    render?: (item: T) => React.ReactNode;
  }>;
  rowHeight?: number;
  containerHeight?: number;
  onRowClick?: (item: T) => void;
  className?: string;
}

export function VirtualizedTable<T extends Record<string, any>>({
  data,
  columns,
  rowHeight = 50,
  containerHeight = 400,
  onRowClick,
  className = '',
}: VirtualizedTableProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / rowHeight);
    const endIndex = Math.min(startIndex + Math.ceil(containerHeight / rowHeight) + 1, data.length);
    return { startIndex, endIndex };
  }, [scrollTop, rowHeight, containerHeight, data.length]);

  // Get visible items
  const visibleItems = useMemo(() => {
    return data.slice(visibleRange.startIndex, visibleRange.endIndex);
  }, [data, visibleRange]);

  // Handle scroll
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  };

  // Total height for scrollbar
  const totalHeight = data.length * rowHeight;

  // Offset for visible items
  const offsetY = visibleRange.startIndex * rowHeight;

  return (
    <div className={`virtualized-table ${className}`}>
      {/* Header */}
      <div className="table-header">
        <div className="table-row header-row">
          {columns.map(column => (
            <div key={column.key} className="table-cell header-cell">
              {column.label}
            </div>
          ))}
        </div>
      </div>

      {/* Scrollable body */}
      <div
        ref={containerRef}
        className="table-body"
        style={{ height: containerHeight, overflow: 'auto' }}
        onScroll={handleScroll}
      >
        {/* Total height container for scrollbar */}
        <div style={{ height: totalHeight, position: 'relative' }}>
          {/* Visible rows */}
          <div
            style={{
              transform: `translateY(${offsetY}px)`,
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
            }}
          >
            {visibleItems.map((item, index) => {
              const actualIndex = visibleRange.startIndex + index;
              return (
                <div
                  key={actualIndex}
                  className="table-row data-row"
                  style={{ height: rowHeight }}
                  onClick={() => onRowClick?.(item)}
                >
                  {columns.map(column => (
                    <div key={column.key} className="table-cell data-cell">
                      {column.render ? column.render(item) : item[column.key]}
                    </div>
                  ))}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

export default VirtualizedTable;
