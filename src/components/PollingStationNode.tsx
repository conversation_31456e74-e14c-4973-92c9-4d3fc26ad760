import React from 'react';
import ChevronRightIcon from './icons/ChevronRightIcon';

interface Section {
  id: string;
  name: string;
  isSelected: boolean;
  voterCount: number;
}

interface PollingStation {
  id: string;
  name: string;
  isOpen: boolean;
  isSelected: boolean;
  sections: Section[];
  voterCount: number;
}

interface PollingStationNodeProps {
  station: PollingStation;
  onToggle: (e: React.MouseEvent, stationId: string) => void;
  onStationSelect: (stationId: string) => void;
  onSectionSelect: (stationId: string, sectionId: string) => void;
}

/**
 * Capitalizes the first letter of each word in a string
 */
const capitalizeWords = (str: string): string => {
  return str.replace(/\b\w+\b/g, word =>
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  );
};

const PollingStationNode: React.FC<PollingStationNodeProps> = ({
  station,
  onToggle,
  onStationSelect,
  onSectionSelect,
}) => {
  return (
    <li>
      <details open={station.isOpen}>
        <summary onClick={e => onToggle(e, station.id)}>
          <label className="checkbox-container" onClick={e => e.stopPropagation()}>
            <input
              type="checkbox"
              checked={station.isSelected}
              onChange={() => onStationSelect(station.id)}
            />
            <span className="checkmark"></span>
            <span className="checkbox-label">
              <span className="station-name">{station.name}</span>
              <span className="voter-count">({station.voterCount})</span>
            </span>
          </label>
          <ChevronRightIcon />
        </summary>
        {station.sections.length > 0 && (
          <ul>
            {station.sections.map(section => (
              <li key={section.id}>
                <label className="checkbox-container child">
                  <input
                    type="checkbox"
                    checked={section.isSelected}
                    onChange={() => onSectionSelect(station.id, section.id)}
                  />
                  <span className="checkmark"></span>
                  <span className="checkbox-label">
                    <span className="section-name">{capitalizeWords(section.name)}</span>
                    <span className="voter-count">({section.voterCount})</span>
                  </span>
                </label>
              </li>
            ))}
          </ul>
        )}
      </details>
    </li>
  );
};

export default PollingStationNode;
