import { useState, useEffect, useCallback, memo } from 'react';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import { useEscapeKey } from '../hooks/useClickOutside';
import { useDialogContext } from './DialogProvider';

import TransactionService, { Transaction } from '../database/TransactionService';
import FamilyService, { FamilyMember } from '../database/FamilyService';
import { FilterUtils } from '../utils/filterUtils';
import EditIcon from './icons/EditIcon';
import DeleteIcon from './icons/DeleteIcon';
import SaveIcon from './icons/SaveIcon';
import CancelIcon from './icons/CancelIcon';

function VoterDetailPanel() {
  const { state, dispatch } = useApp();
  const { canAccessTransactions, canEditVoters } = useAuth();
  const { isVoterPanelOpen, selectedVoter } = state;
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<any>({});
  const [categoryOptions, setCategoryOptions] = useState<{
    community: string[];
    religion: string[];
    economic_status: string[];
    education: string[];
    occupation: string[];
    transaction_purpose: string[];
  }>({
    community: [],
    religion: [],
    economic_status: [],
    education: [],
    occupation: [],
    transaction_purpose: [],
  });
  const [pollingStations, setPollingStations] = useState<string[]>([]);
  const [sections, setSections] = useState<string[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [transactionTotal, setTransactionTotal] = useState<number>(0);
  const [newTransaction, setNewTransaction] = useState<{
    date: string;
    purpose: string;
    amount: string;
  }>({
    date: '',
    purpose: '',
    amount: '',
  });
  const [isAddingTransaction, setIsAddingTransaction] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState<Transaction | null>(null);
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    transaction: Transaction | null;
  }>({
    visible: false,
    x: 0,
    y: 0,
    transaction: null,
  });
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([]);
  const [familyStats, setFamilyStats] = useState<{
    totalMembers: number;
    maleMembers: number;
    femaleMembers: number;
    averageAge: number;
  }>({
    totalMembers: 0,
    maleMembers: 0,
    femaleMembers: 0,
    averageAge: 0,
  });

  // Use database operations from context instead of direct hook
  const { database } = useApp();
  const { showSuccess, showError, showConfirm } = useDialogContext();

  // Close panel on escape key
  useEscapeKey(() => {
    if (isVoterPanelOpen) {
      handleClose();
    }
  });

  const handleClose = () => {
    dispatch({ type: 'TOGGLE_VOTER_PANEL' });
    setIsEditing(false);
  };

  // Initialize form data when voter changes
  useEffect(() => {
    if (selectedVoter) {
      // Auto-start editing for new voters
      if (selectedVoter.id === 0) {
        setIsEditing(true);
      }

      setFormData({
        name: selectedVoter.name || '',
        epic_number: selectedVoter.epic_number || selectedVoter.epic || '',
        relationship_type: selectedVoter.relationship_type || 'Father',
        relationship_name: selectedVoter.relationship_name || selectedVoter.fatherName || '',
        house_number: selectedVoter.house_number || selectedVoter.house_number || '',
        age: selectedVoter.age || '',
        gender: selectedVoter.gender || '',
        polling_station: selectedVoter.polling_station || selectedVoter.pollingStation || '',
        section: selectedVoter.section || '',
        status: selectedVoter.status || '',
        supporter_status: selectedVoter.supporter_status || '',
        education: selectedVoter.education || '',
        occupation: selectedVoter.occupation || selectedVoter.qualification || '',
        community: selectedVoter.community || '',
        religion: selectedVoter.religion || '',
        economic_status: selectedVoter.economic_status || selectedVoter.economicStatus || '',
        phone: selectedVoter.phone || '',
        email: selectedVoter.email || '',
        facebook: selectedVoter.facebook || '',
        instagram: selectedVoter.instagram || '',
        twitter: selectedVoter.twitter || '',
        custom_notes: selectedVoter.custom_notes || '',
      });
    }
  }, [selectedVoter]);

  // Load category options and polling data
  useEffect(() => {
    const loadOptions = async () => {
      try {
        // Load category options using FilterUtils
        const categoryOptions = await FilterUtils.getVoterDetailCategoryOptions();
        setCategoryOptions(categoryOptions);

        // Load polling stations using FilterUtils
        const pollingStationsList = await FilterUtils.getPollingStations();
        setPollingStations(pollingStationsList);
      } catch (error) {
        console.error('Failed to load options:', error);
      }
    };

    if (isVoterPanelOpen) {
      loadOptions();
    }
  }, [isVoterPanelOpen]);

  // Load sections when polling station changes
  useEffect(() => {
    const loadSections = async () => {
      if (formData.polling_station) {
        try {
          const sectionsList = await FilterUtils.getSectionsForPollingStation(
            formData.polling_station
          );
          setSections(sectionsList);
        } catch (error) {
          console.error('Failed to load sections:', error);
          setSections([]);
        }
      } else {
        setSections([]);
      }
    };

    loadSections();
  }, [formData.polling_station]);

  // Load transactions when voter changes
  useEffect(() => {
    const loadTransactions = async () => {
      if (selectedVoter && selectedVoter.id && state.isDatabaseInitialized) {
        try {
          const transactionService = new TransactionService();
          const voterTransactions = await transactionService.getTransactionsForVoter(
            selectedVoter.id
          );
          const total = await transactionService.getTotalForVoter(selectedVoter.id);
          setTransactions(voterTransactions);
          setTransactionTotal(total);
        } catch (error) {
          console.error('Failed to load transactions:', error);
          setTransactions([]);
          setTransactionTotal(0);
        }
      } else {
        setTransactions([]);
        setTransactionTotal(0);
      }
    };

    loadTransactions();
  }, [selectedVoter, state.isDatabaseInitialized]);

  // Load family members when voter changes
  useEffect(() => {
    const loadFamilyMembers = async () => {
      if (selectedVoter && state.isDatabaseInitialized) {
        try {
          const familyService = new FamilyService();
          const members = await familyService.getFamilyMembers(selectedVoter);
          const stats = await familyService.getFamilyStats(selectedVoter);
          setFamilyMembers(members);
          setFamilyStats(stats);
        } catch (error) {
          console.error('Failed to load family members:', error);
          setFamilyMembers([]);
          setFamilyStats({
            totalMembers: 0,
            maleMembers: 0,
            femaleMembers: 0,
            averageAge: 0,
          });
        }
      } else {
        setFamilyMembers([]);
        setFamilyStats({
          totalMembers: 0,
          maleMembers: 0,
          femaleMembers: 0,
          averageAge: 0,
        });
      }
    };

    loadFamilyMembers();
  }, [selectedVoter, state.isDatabaseInitialized]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    if (selectedVoter) {
      setFormData({
        name: selectedVoter.name || '',
        epic_number: selectedVoter.epic_number || selectedVoter.epic || '',
        relationship_type: selectedVoter.relationship_type || 'Father',
        relationship_name: selectedVoter.relationship_name || selectedVoter.fatherName || '',
        house_number: selectedVoter.house_number || selectedVoter.house_number || '',
        age: selectedVoter.age || '',
        gender: selectedVoter.gender || '',
        polling_station: selectedVoter.polling_station || selectedVoter.pollingStation || '',
        section: selectedVoter.section || '',
        status: selectedVoter.status || '',
        supporter_status: selectedVoter.supporter_status || '',
        education: selectedVoter.education || '',
        occupation: selectedVoter.occupation || selectedVoter.qualification || '',
        community: selectedVoter.community || '',
        religion: selectedVoter.religion || '',
        economic_status: selectedVoter.economic_status || selectedVoter.economicStatus || '',
        phone: selectedVoter.phone || '',
        email: selectedVoter.email || '',
        facebook: selectedVoter.facebook || '',
        instagram: selectedVoter.instagram || '',
        twitter: selectedVoter.twitter || '',
        custom_notes: selectedVoter.custom_notes || '',
      });
    }
    setIsEditing(false);
  };

  const validateFormData = () => {
    const errors: string[] = [];

    // Required fields validation
    if (!formData.name?.trim()) {
      errors.push('• Voter name is required');
    }

    if (!formData.relationship_type?.trim()) {
      errors.push('• Relationship type is required');
    }

    if (!formData.relationship_name?.trim()) {
      errors.push('• Relation name is required');
    }

    if (!formData.gender?.trim()) {
      errors.push('• Gender is required');
    }

    if (!formData.epic_number?.trim()) {
      errors.push('• EPIC number is required');
    } else {
      // EPIC validation: 3 uppercase letters + 7 numbers
      const epicPattern = /^[A-Z]{3}[0-9]{7}$/;
      if (!epicPattern.test(formData.epic_number.trim().toUpperCase())) {
        errors.push(
          '• EPIC number must be 3 uppercase letters followed by 7 numbers (e.g., **********)'
        );
      }
    }

    if (!formData.house_number?.trim()) {
      errors.push('• House number/address is required');
    }

    if (!formData.age || formData.age < 18 || formData.age > 120) {
      errors.push('• Age must be between 18 and 120 years');
    }

    if (!formData.polling_station?.trim()) {
      errors.push('• Polling station is required');
    }

    if (!formData.section?.trim()) {
      errors.push('• Section is required');
    }

    // Contact validation (optional but if provided, must be valid)
    if (formData.phone?.trim()) {
      // Indian phone validation: 10 digits or landline format
      const phonePattern = /^(\+91[-\s]?)?[0]?(91)?[6789]\d{9}$|^[0-9]{2,4}-[0-9]{6,8}$/;
      if (!phonePattern.test(formData.phone.trim().replace(/[-\s]/g, ''))) {
        errors.push('• Phone number format is invalid (use 10-digit mobile or landline format)');
      }
    }

    if (formData.email?.trim()) {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(formData.email.trim())) {
        errors.push('• Email format is invalid');
      }
    }

    // Social media validation (just check for valid usernames)
    if (formData.facebook?.trim() && formData.facebook.includes(' ')) {
      errors.push('• Facebook username should not contain spaces');
    }

    if (formData.instagram?.trim() && formData.instagram.includes(' ')) {
      errors.push('• Instagram username should not contain spaces');
    }

    if (formData.twitter?.trim() && formData.twitter.includes(' ')) {
      errors.push('• Twitter username should not contain spaces');
    }

    return errors;
  };

  const handleDelete = async () => {
    if (!selectedVoter || selectedVoter.id === 0) return;

    const message = `This will permanently delete the voter "${selectedVoter.name}" and will also delete:\n• All associated transactions\n• Family relationship references\n\nThis action cannot be undone.`;

    showConfirm({
      title: 'Delete Confirmation',
      message,
      variant: 'danger',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: async () => {
        try {
          await database.deleteVoter(selectedVoter.id);

          // Close the panel
          dispatch({ type: 'TOGGLE_VOTER_PANEL' });
          setIsEditing(false);

          showSuccess('Success', 'Voter deleted successfully');
        } catch (error) {
          console.error('❌ Failed to delete voter:', error);
          showError(
            'Delete Failed',
            `Failed to delete voter: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
        }
      },
    });
  };

  const handleSave = async () => {
    if (!selectedVoter || !state.isDatabaseInitialized) return;

    try {
      // Validate all form data
      const validationErrors = validateFormData();
      console.log('Validation Errors:', validationErrors);
      if (validationErrors.length > 0) {
        showError(
          'Validation Error',
          'Please fix the following errors:\n\n' + validationErrors.join('\n')
        );
        return;
      }

      // Option 4: Auto-delete for invalid statuses
      if (formData.status === 'Disqualified' || formData.status === 'Duplicate') {
        const statusAction =
          formData.status === 'Disqualified' ? 'disqualified' : 'marked as duplicate';
        const message = `This voter will be ${statusAction} and permanently deleted from the database.\n\nVoter: "${formData.name}"\nReason: ${formData.status}`;

        showConfirm({
          title: 'Delete Invalid Voter',
          message,
          variant: 'danger',
          confirmText: 'Delete',
          onConfirm: async () => {
            if (selectedVoter.id !== 0) {
              try {
                await database.deleteVoter(selectedVoter.id);
                dispatch({ type: 'TOGGLE_VOTER_PANEL' });
                setIsEditing(false);
                showSuccess('Success', `Voter ${statusAction} and deleted successfully`);
              } catch (error) {
                showError(
                  'Delete Failed',
                  `Failed to delete voter: ${error instanceof Error ? error.message : 'Unknown error'}`
                );
              }
            }
          },
        });
        return;
      }

      // Validate polling station and section - only allow existing ones
      if (formData.polling_station && !pollingStations.includes(formData.polling_station)) {
        showError(
          'Invalid Polling Station',
          'Please select a valid polling station from the available options. New polling stations can only be created through data import.'
        );
        return;
      }

      if (formData.section && !sections.includes(formData.section)) {
        showError(
          'Invalid Section',
          'Please select a valid section for the selected polling station. New sections can only be created through data import.'
        );
        return;
      }

      // Prepare voter data for update - clean and validate data
      const { age, ...restFormData } = formData;

      // Convert age to birth_year with proper validation
      let birth_year: number | undefined = undefined;
      if (age && age.toString().trim()) {
        const ageNum = parseInt(age.toString().trim());
        if (!isNaN(ageNum) && ageNum > 0 && ageNum < 150) {
          birth_year = new Date().getFullYear() - ageNum;
        }
      }

      // Clean up the data - remove empty strings and convert to null/undefined
      const cleanedData = Object.entries(restFormData).reduce((acc, [key, value]) => {
        if (value === '' || value === null) {
          acc[key] = undefined;
        } else if (typeof value === 'string') {
          acc[key] = value.trim() || undefined;
        } else {
          acc[key] = value;
        }
        return acc;
      }, {} as any);

      const updatedVoterData = {
        ...cleanedData,
        birth_year,
        // Ensure required fields are not undefined
        name: formData.name?.trim().toUpperCase(),
        epic_number: formData.epic_number?.trim().toUpperCase(),
        relationship_type: formData.relationship_type || 'Father',
        relationship_name: formData.relationship_name?.trim().toUpperCase(),
        gender: formData.gender || 'Male',
      };

      let finalVoter;

      if (selectedVoter.id === 0) {
        // Adding new voter
        const newVoterId = await database.addVoter(updatedVoterData);

        finalVoter = {
          ...updatedVoterData,
          id: newVoterId,
          // Add backward compatibility fields
          age: birth_year ? new Date().getFullYear() - birth_year : undefined,
          epic: updatedVoterData.epic_number,
          pollingStation: updatedVoterData.polling_station,
          economicStatus: updatedVoterData.economic_status,
          fatherName: updatedVoterData.relationship_name,
          qualification: updatedVoterData.occupation,
        };
      } else {
        // Updating existing voter
        await database.updateVoter(selectedVoter.id, updatedVoterData);

        finalVoter = {
          ...selectedVoter,
          ...updatedVoterData,
          // Add backward compatibility fields
          age: birth_year ? new Date().getFullYear() - birth_year : undefined,
          epic: updatedVoterData.epic_number,
          pollingStation: updatedVoterData.polling_station,
          economicStatus: updatedVoterData.economic_status,
          fatherName: updatedVoterData.relationship_name,
          qualification: updatedVoterData.occupation,
        };

        // Update the voter in the context (only for existing voters)
        dispatch({
          type: 'UPDATE_VOTER',
          payload: finalVoter,
        });
      }

      // Update the selected voter
      dispatch({
        type: 'SET_SELECTED_VOTER',
        payload: finalVoter,
      });

      setIsEditing(false);

      // Show success message
      showSuccess('Success', selectedVoter.id === 0 ? 'Voter added successfully' : 'Voter updated successfully');
    } catch (error) {
      console.error('❌ Failed to save voter changes:', error);

      // More specific error messages
      let errorMessage = 'Failed to save changes. ';
      if (error instanceof Error) {
        if (error.message.includes('UNIQUE constraint failed')) {
          errorMessage += 'EPIC number already exists for another voter.';
        } else if (error.message.includes('NOT NULL constraint failed')) {
          errorMessage += 'Required field is missing.';
        } else {
          errorMessage += error.message;
        }
      } else {
        errorMessage += 'Please try again.';
      }

      showError('Save Failed', errorMessage);
    }
  };

  const handleInputChange = useCallback((field: string, value: string) => {
    // Auto-capitalize specific fields for database consistency
    let processedValue = value;
    if (field === 'name' || field === 'relationship_name' || field === 'epic_number') {
      processedValue = value.toUpperCase();
    }

    setFormData((prev: any) => ({
      ...prev,
      [field]: processedValue,
    }));
  }, []);

  // Transaction handlers
  const handleAddTransaction = () => {
    setEditingTransaction(null);
    setIsAddingTransaction(true);
    setNewTransaction({
      date: new Date().toLocaleDateString('en-GB'), // DD/MM/YYYY format
      purpose: '',
      amount: '',
    });
  };

  const handleSaveTransaction = async () => {
    if (!selectedVoter || !state.isDatabaseInitialized) return;

    try {
      // Validate fields
      if (
        !newTransaction.date.trim() ||
        !newTransaction.purpose.trim() ||
        !newTransaction.amount.trim()
      ) {
        showError('Validation Error', 'All fields are required');
        return;
      }

      // Convert date from DD/MM/YYYY to DD-MM-YYYY
      const dateFormatted = newTransaction.date.replace(/\//g, '-');

      // Validate and parse amount
      const amount = parseInt(newTransaction.amount);
      if (isNaN(amount) || amount <= 0 || amount > 699999) {
        alert('Amount must be between 1 and 699999');
        return;
      }

      const transactionService = new TransactionService();
      await transactionService.addTransaction({
        voter_id: selectedVoter.id,
        date: dateFormatted,
        purpose: newTransaction.purpose.trim(),
        amount: amount,
      });

      // Reload transactions
      const voterTransactions = await transactionService.getTransactionsForVoter(selectedVoter.id);
      const total = await transactionService.getTotalForVoter(selectedVoter.id);
      setTransactions(voterTransactions);
      setTransactionTotal(total);

      // Reset form
      setIsAddingTransaction(false);
      setNewTransaction({ date: '', purpose: '', amount: '' });
    } catch (error) {
      console.error('Failed to save transaction:', error);
      alert(
        'Failed to save transaction: ' + (error instanceof Error ? error.message : 'Unknown error')
      );
    }
  };

  const handleCancelTransaction = () => {
    setIsAddingTransaction(false);
    setNewTransaction({ date: '', purpose: '', amount: '' });
  };

  const handleSaveEditTransaction = async () => {
    if (!selectedVoter || !editingTransaction || !state.isDatabaseInitialized) return;

    try {
      // Validate fields
      if (
        !editingTransaction.date.trim() ||
        !editingTransaction.purpose.trim() ||
        !editingTransaction.amount
      ) {
        alert('All fields are required');
        return;
      }

      // Convert date from DD/MM/YYYY to DD-MM-YYYY
      const dateFormatted = editingTransaction.date.replace(/\//g, '-');

      // Validate amount
      if (editingTransaction.amount <= 0 || editingTransaction.amount > 699999) {
        alert('Amount must be between 1 and 699999');
        return;
      }

      const transactionService = new TransactionService();
      await transactionService.updateTransaction(editingTransaction.id!, {
        date: dateFormatted,
        purpose: editingTransaction.purpose.trim(),
        amount: editingTransaction.amount,
      });

      // Reload transactions
      const voterTransactions = await transactionService.getTransactionsForVoter(selectedVoter.id);
      const total = await transactionService.getTotalForVoter(selectedVoter.id);
      setTransactions(voterTransactions);
      setTransactionTotal(total);

      // Reset edit state
      setEditingTransaction(null);
    } catch (error) {
      console.error('Failed to update transaction:', error);
      alert(
        'Failed to update transaction: ' + (error instanceof Error ? error.message : 'Unknown error')
      );
    }
  };

  const handleDeleteTransaction = async (transactionId: number) => {
    if (!selectedVoter || !state.isDatabaseInitialized) return;

    showConfirm({
      title: 'Delete Transaction',
      message: 'Are you sure you want to delete this transaction?\n\nThis action cannot be undone.',
      variant: 'danger',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: async () => {
        try {
          const transactionService = new TransactionService();
          await transactionService.deleteTransaction(transactionId);

          // Reload transactions
          const voterTransactions = await transactionService.getTransactionsForVoter(
            selectedVoter.id
          );
          const total = await transactionService.getTotalForVoter(selectedVoter.id);
          setTransactions(voterTransactions);
          setTransactionTotal(total);
        } catch (error) {
          console.error('Failed to delete transaction:', error);
          showError('Delete Failed', 'Failed to delete transaction');
        }
      },
    });
  };

  const formatAmount = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  // Context menu handlers
  const handleContextMenu = (e: React.MouseEvent, transaction: Transaction) => {
    e.preventDefault();
    const panel = (e.currentTarget as HTMLElement).closest('.voter-detail-panel');
    const rect = panel ? panel.getBoundingClientRect() : { left: 0, top: 0 };

    setContextMenu({
      visible: true,
      x: e.clientX - rect.left,
      y: e.clientY,
      transaction,
    });
  };

  const handleContextMenuEdit = () => {
    if (contextMenu.transaction) {
      setIsAddingTransaction(false);
      setEditingTransaction(contextMenu.transaction);
      setContextMenu({ visible: false, x: 0, y: 0, transaction: null });
    }
  };

  const handleContextMenuDelete = () => {
    if (contextMenu.transaction) {
      handleDeleteTransaction(contextMenu.transaction.id!);
      setContextMenu({ visible: false, x: 0, y: 0, transaction: null });
    }
  };

  const handleCloseContextMenu = () => {
    setContextMenu({ visible: false, x: 0, y: 0, transaction: null });
  };

  // Close context menu when clicking outside or on escape
  useEffect(() => {
    const handleClickOutside = () => handleCloseContextMenu();
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') handleCloseContextMenu();
    };

    if (contextMenu.visible) {
      document.addEventListener('click', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [contextMenu.visible]);

  // Always render for smooth animations - don't conditionally render
  if (!selectedVoter) {
    return (
      <div className="voter-detail-panel">
        <div className="panel-header">
          <h2 className="panel-title">Voter Details</h2>
          <div className="panel-actions">
            <button onClick={handleClose} className="panel-close" aria-label="Close panel">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>
        <div className="panel-content">
          <p>No voter selected</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`voter-detail-panel ${isVoterPanelOpen ? 'is-open' : ''}`}>
      <div className="panel-header">
        <h2 className="panel-title">Voter Details</h2>
        <div className="panel-actions">
          {canEditVoters() && (
            <>
              <button
                onClick={handleEdit}
                className="btn btn-edit"
                style={{ display: isEditing ? 'none' : 'inline-flex' }}
              >
                <EditIcon />
                Edit
              </button>
              <button
                onClick={handleDelete}
                className="btn btn-danger"
                style={{ display: isEditing || selectedVoter?.id === 0 ? 'none' : 'inline-flex' }}
                title="Delete voter permanently"
              >
                <DeleteIcon />
                Delete
              </button>
            </>
          )}
          <button
            onClick={handleSave}
            className="btn btn-primary btn-save"
            style={{ display: isEditing ? 'inline-flex' : 'none' }}
          >
            <SaveIcon />
            Save
          </button>
          <button
            onClick={handleCancel}
            className="btn btn-cancel"
            style={{ display: isEditing ? 'inline-flex' : 'none' }}
          >
            <CancelIcon />
            Cancel
          </button>
          <button onClick={handleClose} className="panel-close" aria-label="Close panel">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <div className="panel-content">
        {/* Voter Profile Section */}
        <section className="panel-section">
          <details open>
            <summary>
              <h3 className="section-title">Voter Profile</h3>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </summary>
            <div className="section-grid">
              <EditableField
                label="Voter's Name"
                field="name"
                value={formData.name}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="EPIC Number"
                field="epic_number"
                value={formData.epic_number}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="Relationship Type"
                field="relationship_type"
                type="select"
                options={['Father', 'Mother', 'Husband', 'Others']}
                value={formData.relationship_type}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="Relation Name"
                field="relationship_name"
                value={formData.relationship_name}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="House Number"
                field="house_number"
                value={formData.house_number}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="Age"
                field="age"
                value={formData.age}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="Gender"
                field="gender"
                type="select"
                options={['Male', 'Female', 'Other']}
                value={formData.gender}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="Voter Status"
                field="status"
                type="select"
                options={['Active', 'Expired', 'Shifted', 'Duplicate', 'Missing', 'Disqualified']}
                value={formData.status}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="Polling Station"
                field="polling_station"
                type="select"
                options={pollingStations}
                value={formData.polling_station}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="Section"
                field="section"
                type="select"
                options={sections}
                value={formData.section}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
            </div>
          </details>
        </section>

        {/* Combined Section for Status, Demographics and Notes */}
        <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Additional Information</h3>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </summary>
            {/* Status Section */}
            <div className="panel-subsection">
              <h4 className="subsection-title">Voter Affiliation</h4>
              <div className="status-grid">
                <div className="status-item">
                  <span className="status-indicator"></span>
                  <label className="status-label" htmlFor="supporter_status">
                    Support Status
                  </label>
                  <select
                    id="supporter_status"
                    className={`status-value ${isEditing ? 'editable' : 'readonly'}`}
                    value={formData.supporter_status || ''}
                    onChange={e => handleInputChange('supporter_status', e.target.value)}
                    disabled={!isEditing}
                  >
                    <option value="">Select Status</option>
                    <option value="Strong Supporter">Strong Supporter</option>
                    <option value="Potential Supporter">Potential Supporter</option>
                    <option value="Undecided">Undecided</option>
                    <option value="Opposed">Opposed</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Demographics Section */}
            <div className="panel-subsection">
              <h4 className="subsection-title">Demographics</h4>
              <div className="section-grid">
                <EditableField
                  label="Education"
                  field="education"
                  type="select"
                  options={categoryOptions.education}
                  value={formData.education}
                  isEditing={isEditing}
                  handleInputChange={handleInputChange}
                />
                <EditableField
                  label="Occupation"
                  field="occupation"
                  type="select"
                  options={categoryOptions.occupation}
                  value={formData.occupation}
                  isEditing={isEditing}
                  handleInputChange={handleInputChange}
                />
                <EditableField
                  label="Community"
                  field="community"
                  type="select"
                  options={categoryOptions.community}
                  value={formData.community}
                  isEditing={isEditing}
                  handleInputChange={handleInputChange}
                />
                <EditableField
                  label="Religion"
                  field="religion"
                  type="select"
                  options={categoryOptions.religion}
                  value={formData.religion}
                  isEditing={isEditing}
                  handleInputChange={handleInputChange}
                />
                <EditableField
                  label="Economic Status"
                  field="economic_status"
                  type="select"
                  options={categoryOptions.economic_status}
                  value={formData.economic_status}
                  isEditing={isEditing}
                  handleInputChange={handleInputChange}
                />
              </div>
            </div>

            {/* Notes Section */}
            <div className="panel-subsection">
              <h4 className="subsection-title">Notes</h4>
              <EditableField
                label="Custom Notes"
                field="custom_notes"
                type="textarea"
                value={formData.custom_notes}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
            </div>
          </details>
        </section>

        {/* Contact Details Section */}
        <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Contact Details</h3>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </summary>
            <div className="section-grid">
              <EditableField
                label="Phone"
                field="phone"
                value={formData.phone}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="Email"
                field="email"
                value={formData.email}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="Facebook"
                field="facebook"
                value={formData.facebook}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="Instagram"
                field="instagram"
                value={formData.instagram}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
              <EditableField
                label="Twitter"
                field="twitter"
                value={formData.twitter}
                isEditing={isEditing}
                handleInputChange={handleInputChange}
              />
            </div>
          </details>
        </section>

        {/* Family Members Section */}
        <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Family Members</h3>
              <div className="summary-right">
                <span className="badge badge-count">{familyStats.totalMembers} members</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </div>
            </summary>
            <div className="family-section">
              <div className="family-members">
                {familyMembers.length > 0 ? (
                  familyMembers.map(member => (
                    <div key={member.id} className="family-member">
                      <div className="member-info">
                        <span className="member-name">{member.name}</span>
                        <span className="member-details">
                          {member.age && `${member.age}y`} • {member.gender}
                          {/* {member.house_number && ` • House ${member.house_number}`} */}
                        </span>
                      </div>
                      <div className="member-relationship">
                        <span className="member-relation">{member.relationshipToCurrentVoter}</span>
                        {member.relationship_type && member.relationship_name && (
                          <span className="member-parent">
                            {member.relationship_type}: {member.relationship_name}
                          </span>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="no-family-members">
                    <span style={{ color: '#999', fontStyle: 'italic' }}>
                      No family members found
                    </span>
                    <p style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                      Family members are detected based on house number, father's name, and
                      relationships.
                    </p>
                  </div>
                )}
              </div>

              {familyStats.totalMembers > 0 && (
                <div className="family-stats">
                  <div className="stat-item">
                    <span className="stat-label">Total:</span>
                    <span className="stat-value">{familyStats.totalMembers}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Male:</span>
                    <span className="stat-value">{familyStats.maleMembers}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Female:</span>
                    <span className="stat-value">{familyStats.femaleMembers}</span>
                  </div>
                  {familyStats.averageAge > 0 && (
                    <div className="stat-item">
                      <span className="stat-label">Avg Age:</span>
                      <span className="stat-value">{familyStats.averageAge}</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </details>
        </section>

        {/* Transactions Section - Admin Only */}
        {canAccessTransactions() && (
          <section className="panel-section">
          <details>
            <summary>
              <h3 className="section-title">Transactions</h3>
              <div className="summary-right">
                <span className="badge badge-amount">{formatAmount(transactionTotal)}</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="m9 18 6-6-6-6"></path>
                </svg>
              </div>
            </summary>
            <div className="transactions-table">
              <table>
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Purpose</th>
                    <th>Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {isAddingTransaction && (
                    <tr>
                      <td>
                        <input
                          type="text"
                          value={newTransaction.date}
                          onChange={e =>
                            setNewTransaction(prev => ({ ...prev, date: e.target.value }))
                          }
                          placeholder="DD-MM-YYYY"
                          className="detail-value editable"
                          style={{ width: '100%' }}
                        />
                      </td>
                      <td>
                        <div className="select-wrapper">
                          <select
                            value={newTransaction.purpose}
                            onChange={e =>
                              setNewTransaction(prev => ({ ...prev, purpose: e.target.value }))
                            }
                            className="detail-value editable"
                            style={{ width: '100%' }}
                          >
                            <option value="">Select Purpose</option>
                            {categoryOptions.transaction_purpose.map(purpose => (
                              <option key={purpose} value={purpose}>
                                {purpose}
                              </option>
                            ))}
                          </select>
                        </div>
                      </td>
                      <td>
                        <input
                          type="number"
                          value={newTransaction.amount}
                          onChange={e => {
                            const value = e.target.value;
                            if (value === '' || (parseInt(value) >= 1 && parseInt(value) <= 699999)) {
                              setNewTransaction(prev => ({ ...prev, amount: value }));
                            }
                          }}
                          placeholder="Amount"
                          className="detail-value editable"
                          style={{ width: '100%' }}
                          min="1"
                          max="699999"
                          step="1"
                        />
                      </td>
                    </tr>
                  )}
                  {transactions.map(transaction => (
                    <tr
                      key={transaction.id}
                      onContextMenu={(e) => handleContextMenu(e, transaction)}
                      style={{ cursor: 'context-menu' }}
                    >
                      {editingTransaction?.id === transaction.id ? (
                        // Edit mode
                        <>
                          <td>
                            <input
                              type="text"
                              value={editingTransaction?.date || ''}
                              onChange={e =>
                                setEditingTransaction(prev => prev ? ({ ...prev, date: e.target.value }) : null)
                              }
                              placeholder="DD-MM-YYYY"
                              className="detail-value editable"
                              style={{ width: '100%' }}
                            />
                          </td>
                          <td>
                            <div className="select-wrapper">
                              <select
                                value={editingTransaction?.purpose || ''}
                                onChange={e =>
                                  setEditingTransaction(prev => prev ? ({ ...prev, purpose: e.target.value }) : null)
                                }
                                className="detail-value editable"
                                style={{ width: '100%' }}
                              >
                                <option value="">Select Purpose</option>
                                {categoryOptions.transaction_purpose.map(purpose => (
                                  <option key={purpose} value={purpose}>
                                    {purpose}
                                  </option>
                                ))}
                              </select>
                            </div>
                          </td>
                          <td>
                            <input
                              type="number"
                              value={editingTransaction?.amount || ''}
                              onChange={e => {
                                const value = e.target.value;
                                if (value === '' || (parseInt(value) >= 1 && parseInt(value) <= 699999)) {
                                  setEditingTransaction(prev => prev ? ({ ...prev, amount: parseInt(value) || 0 }) : null);
                                }
                              }}
                              placeholder="Amount"
                              className="detail-value editable"
                              style={{ width: '100%' }}
                              min="1"
                              max="699999"
                              step="1"
                            />
                          </td>
                        </>
                      ) : (
                        // View mode
                        <>
                          <td>{transaction.date}</td>
                          <td>{transaction.purpose}</td>
                          <td>{formatAmount(transaction.amount)}</td>
                        </>
                      )}
                    </tr>
                  ))}
                  {transactions.length === 0 && !isAddingTransaction && (
                    <tr>
                      <td
                        colSpan={3}
                        style={{
                          textAlign: 'center',
                          color: '#999',
                          fontStyle: 'italic',
                          padding: '20px',
                        }}
                      >
                        Nothing to show
                      </td>
                    </tr>
                  )}
                </tbody>
                {transactions.length > 0 && (
                  <tfoot>
                    <tr>
                      <td colSpan={2}>Total</td>
                      <td>{formatAmount(transactionTotal)}</td>
                    </tr>
                  </tfoot>
                )}
              </table>

              {/* Edit mode controls */}
              {editingTransaction && (
                <div className="transaction-controls">
                  <button
                    onClick={() => handleSaveEditTransaction()}
                    className="btn btn-primary"
                    title="Save changes"
                  >
                    <SaveIcon style={{ marginRight: '4px' }} />
                    Save Changes
                  </button>
                  <button
                    onClick={() => setEditingTransaction(null)}
                    className="btn btn-cancel"
                    title="Cancel edit"
                  >
                    <CancelIcon style={{ marginRight: '4px' }} />
                    Cancel
                  </button>
                </div>
              )}

              <div className="transaction-controls">
                <button
                  onClick={handleAddTransaction}
                  className="btn btn-edit"
                  style={{ display: isAddingTransaction || editingTransaction ? 'none' : 'inline-flex' }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                  Add
                </button>
                <button
                  onClick={handleSaveTransaction}
                  className="btn btn-primary btn-save"
                  style={{ display: isAddingTransaction ? 'inline-flex' : 'none' }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                    <polyline points="17 21 17 13 7 13 7 21"></polyline>
                    <polyline points="7 3 7 8 15 8"></polyline>
                  </svg>
                  Save
                </button>
                <button
                  onClick={handleCancelTransaction}
                  className="btn btn-cancel"
                  style={{ display: isAddingTransaction ? 'inline-flex' : 'none' }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M18 6L6 18"></path>
                    <path d="M6 6l12 12"></path>
                  </svg>
                  Cancel
                </button>
              </div>
            </div>
          </details>
        </section>
        )}
      </div>

      {/* Context Menu */}
      {contextMenu.visible && (
        <div
          className={`context-menu ${contextMenu.visible ? 'is-active' : ''}`}
          style={{
            top: contextMenu.y,
            left: contextMenu.x,
          }}
        >
          <button
            onClick={handleContextMenuEdit}
            className="context-menu-item"
          >
            <EditIcon /> Edit
          </button>
          <button
            onClick={handleContextMenuDelete}
            className="context-menu-item context-menu-item-delete"
          >
            <DeleteIcon /> Delete
          </button>
        </div>
      )}
    </div>
  );
}

export default VoterDetailPanel;

// Consistent form field component - always renders form elements, just toggles disabled state
const EditableField = memo(
  ({
    label,
    field,
    value,
    isEditing,
    handleInputChange,
    type = 'text',
    options = null,
  }: {
    label: string;
    field: string;
    value: string;
    isEditing: boolean;
    handleInputChange: (field: string, value: string) => void;
    type?: 'text' | 'select' | 'textarea';
    options?: string[] | null;
  }) => {
    if (type === 'select' && options) {
      return (
        <div className="detail-item">
          <label className="detail-label" htmlFor={field}>
            {label}
          </label>
          <div className="select-wrapper">
            <select
              id={field}
              className={`detail-value ${isEditing ? 'editable' : 'readonly'}`}
              value={value || ''}
              onChange={e => handleInputChange(field, e.target.value)}
              disabled={!isEditing}
            >
              <option value="">Select {label}</option>
              {options.map(option => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        </div>
      );
    }

    if (type === 'textarea') {
      return (
        <div className="detail-item">
          <label className="detail-label" htmlFor={field}>
            {label}
          </label>
          <textarea
            id={field}
            className={`detail-value ${isEditing ? 'editable' : 'readonly'}`}
            value={value || ''}
            onChange={e => handleInputChange(field, e.target.value)}
            disabled={!isEditing}
            rows={3}
            placeholder={isEditing ? `Enter ${label.toLowerCase()}` : ''}
          />
        </div>
      );
    }

    return (
      <div className="detail-item">
        <label className="detail-label" htmlFor={field}>
          {label}
        </label>
        <input
          id={field}
          type="text"
          className={`detail-value ${isEditing ? 'editable' : 'readonly'}`}
          value={value || ''}
          onChange={e => handleInputChange(field, e.target.value)}
          disabled={!isEditing}
          placeholder={isEditing ? `Enter ${label.toLowerCase()}` : ''}
        />
      </div>
    );
  }
);
