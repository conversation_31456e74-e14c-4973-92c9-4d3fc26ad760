import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';

interface ToolbarProps {
  onFilterToggle: () => void;
  onAddVoter: () => void;
  isFilterPanelOpen: boolean;
}

function Toolbar({ onFilterToggle, onAddVoter, isFilterPanelOpen }: ToolbarProps) {
  const { theme, toggleTheme } = useTheme();
  const { canEditVoters } = useAuth();

  return (
    <div className="toolbar">
      <div className="toolbar-left">
        <h1>Voter Database</h1>
      </div>
      <div className="toolbar-right">
        <div className="theme-toggle">
          <button onClick={toggleTheme} className="btn" aria-label="Toggle theme">
            {theme === 'light' ? (
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M12 2v2" />
                <path d="M13 8.129A4 4 0 0 1 15.873 11" />
                <path d="m19 5-1.256 1.256" />
                <path d="M20 12h2" />
                <path d="M9 8a5 5 0 1 0 7 7 7 7 0 1 1-7-7" />
              </svg>
            ) : (
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="12" cy="12" r="5" />
                <path d="M12 1v2" />
                <path d="M12 21v2" />
                <path d="M4.22 4.22l1.42 1.42" />
                <path d="M18.36 18.36l1.42 1.42" />
                <path d="M1 12h2" />
                <path d="M21 12h2" />
                <path d="M4.22 19.78l1.42-1.42" />
                <path d="M18.36 5.64l1.42-1.42" />
              </svg>
            )}
            {theme === 'light' ? 'Light' : 'Dark'}
          </button>
        </div>
        <button
          onClick={onFilterToggle}
          className={`btn filter-btn ${isFilterPanelOpen ? 'is-active' : ''}`}
          aria-label="Show filter options"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
          </svg>
          Filter
        </button>
        {canEditVoters() && (
          <button onClick={onAddVoter} className="btn btn-primary" aria-label="Add voter">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            Add Voter
          </button>
        )}
      </div>
    </div>
  );
}

export default Toolbar;
