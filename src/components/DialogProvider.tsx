import React, { createContext, useContext } from 'react';
import { useDialog } from '../hooks/useDialog';
import { ConfirmDialog, AlertDialog } from './Dialog';

interface DialogContextType {
  showConfirm: (options: {
    title: string;
    message: string;
    onConfirm: () => void;
    variant?: 'default' | 'danger';
    confirmText?: string;
    cancelText?: string;
  }) => void;
  showAlert: (options: {
    title: string;
    message: string;
    variant?: 'success' | 'error' | 'info';
  }) => void;
  showSuccess: (title: string, message: string) => void;
  showError: (title: string, message: string) => void;
  showDeleteConfirm: (itemName: string, onConfirm: () => void) => void;
}

const DialogContext = createContext<DialogContextType | undefined>(undefined);

export function DialogProvider({ children }: { children: React.ReactNode }) {
  const {
    confirmDialog,
    alertDialog,
    showConfirm,
    hideConfirm,
    showAlert,
    hideAlert,
    showSuccess,
    showError,
    showDeleteConfirm,
  } = useDialog();

  const contextValue: DialogContextType = {
    showConfirm,
    showAlert,
    showSuccess,
    showError,
    showDeleteConfirm,
  };

  return (
    <DialogContext.Provider value={contextValue}>
      {children}

      {/* Confirm Dialog */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={hideConfirm}
        onConfirm={confirmDialog.onConfirm || (() => {})}
        title={confirmDialog.title}
        variant={confirmDialog.variant as 'default' | 'danger'}
        confirmText={confirmDialog.confirmText}
        cancelText={confirmDialog.cancelText}
      >
        {confirmDialog.message}
      </ConfirmDialog>

      {/* Alert Dialog */}
      <AlertDialog
        isOpen={alertDialog.isOpen}
        onClose={hideAlert}
        title={alertDialog.title}
        variant={alertDialog.variant as 'success' | 'error' | 'info'}
      >
        {alertDialog.message}
      </AlertDialog>
    </DialogContext.Provider>
  );
}

export function useDialogContext(): DialogContextType {
  const context = useContext(DialogContext);
  if (context === undefined) {
    throw new Error('useDialogContext must be used within a DialogProvider');
  }
  return context;
}
