import React, { useState, useMemo, useCallback, useRef } from 'react';
import { useApp } from '../context/AppContext';
import { useFilteredVoters } from '../hooks/useFilteredVoters';
import { useClickOutside } from '../hooks/useClickOutside';
import { useVoterTransactions } from '../hooks/useVoterTransactions';

type VoterSortField = 'name' | 'age' | 'gender' | 'epic' | 'pollingStation' | 'affiliation' | 'payee' | 'status';
type SortDirection = 'asc' | 'desc';

function DataTable() {
  const { state, dispatch } = useApp();
  const { voters, filters } = state;

  // Local state for columns dropdown
  const [isColumnsDropdownOpen, setIsColumnsDropdownOpen] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);



  // Sorting state
  const [sortField, setSortField] = useState<VoterSortField>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  // Use global column visibility state
  const { columnVisibility: visibleColumns } = state;

  // Get voter transaction data
  const { getVoterTransactionData } = useVoterTransactions();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 8;

  const filteredVoters = useFilteredVoters(voters, filters);

  // Sort filtered voters based on selected field and direction
  const sortedVoters = useMemo(() => {
    return [...filteredVoters].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      if (sortField === 'name') {
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        const result = aValue.localeCompare(bValue);
        return sortDirection === 'asc' ? result : -result;
      } else if (sortField === 'age') {
        aValue = a.age || 0;
        bValue = b.age || 0;
        const result = aValue - bValue;
        return sortDirection === 'asc' ? result : -result;
      } else if (sortField === 'gender') {
        aValue = a.gender.toLowerCase();
        bValue = b.gender.toLowerCase();
        const result = aValue.localeCompare(bValue);
        return sortDirection === 'asc' ? result : -result;
      } else if (sortField === 'epic') {
        aValue = (a.epic || '').toLowerCase();
        bValue = (b.epic || '').toLowerCase();
        const result = aValue.localeCompare(bValue, undefined, { numeric: true, sensitivity: 'base' });
        return sortDirection === 'asc' ? result : -result;
      } else if (sortField === 'pollingStation') {
        aValue = (a.pollingStation || '').toLowerCase();
        bValue = (b.pollingStation || '').toLowerCase();
        const result = aValue.localeCompare(bValue, undefined, { numeric: true, sensitivity: 'base' });
        return sortDirection === 'asc' ? result : -result;
      } else if (sortField === 'affiliation') {
        const statusOrder = { 'strong supporter': 1, 'potential supporter': 2, 'undecided': 3, 'opposed': 4, 'unknown': 5 };
        aValue = statusOrder[(a.supporter_status || 'unknown').toLowerCase() as keyof typeof statusOrder] || 5;
        bValue = statusOrder[(b.supporter_status || 'unknown').toLowerCase() as keyof typeof statusOrder] || 5;
        const result = aValue - bValue;
        return sortDirection === 'asc' ? result : -result;
      } else if (sortField === 'payee') {
        const aTransactionData = getVoterTransactionData(a.id);
        const bTransactionData = getVoterTransactionData(b.id);
        aValue = aTransactionData.totalAmount;
        bValue = bTransactionData.totalAmount;
        const result = aValue - bValue;
        return sortDirection === 'asc' ? result : -result;
      } else if (sortField === 'status') {
        aValue = (a.status || 'active').toLowerCase();
        bValue = (b.status || 'active').toLowerCase();
        const result = aValue.localeCompare(bValue);
        return sortDirection === 'asc' ? result : -result;
      }

      return 0;
    });
  }, [filteredVoters, sortField, sortDirection, getVoterTransactionData]);

  // Calculate pagination
  const totalPages = Math.ceil(sortedVoters.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentVoters = sortedVoters.slice(startIndex, endIndex);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  // Click outside handler for dropdown
  const dropdownRef = useClickOutside<HTMLDivElement>(() => {
    if (hasInteracted) {
      setIsColumnsDropdownOpen(false);
    }
  });

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch({ type: 'UPDATE_FILTER', payload: { key: 'searchTerm', value: e.target.value } });
  };

  const handleColumnsToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setHasInteracted(true);
    setIsColumnsDropdownOpen(prev => !prev);
  };

  const handleColumnToggle = (column: keyof typeof visibleColumns) => {
    dispatch({ type: 'TOGGLE_COLUMN', payload: column });
  };

  // Handle column header click for sorting
  const handleSort = (field: VoterSortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field with default direction
      setSortField(field);
      setSortDirection(field === 'name' || field === 'gender' || field === 'epic' || field === 'pollingStation' || field === 'status' ? 'asc' : 'desc');
    }
  };

  const handleVoterClick = (voter: any) => {
    dispatch({ type: 'TOGGLE_VOTER_PANEL', payload: voter });
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const startPage = Math.max(1, currentPage - 2);
      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  return (
    <div className="data-table">
      <div className="table-header">
        <span className="table-title">Registered Voters</span>
        <div className="table-controls">
          <div className="search-container">
            <svg
              className="search-icon"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
            <input
              type="text"
              className="search-input"
              placeholder="Search voters..."
              aria-label="Search voters"
              value={filters.searchTerm}
              onChange={handleSearchChange}
            />
          </div>
          <div className="dropdown-container" ref={dropdownRef}>
            <button onClick={handleColumnsToggle} className="btn" aria-label="Show/hide columns">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="9" y1="9" x2="9" y2="21"></line>
                <line x1="15" y1="9" x2="15" y2="21"></line>
              </svg>
              Columns
            </button>
            <div className={`dropdown-menu ${isColumnsDropdownOpen ? 'is-open' : ''}`}>
              {Object.entries(visibleColumns).map(([column, isVisible]) => (
                <div
                  key={column}
                  onClick={() => handleColumnToggle(column as keyof typeof visibleColumns)}
                  className={`dropdown-item ${isVisible ? 'is-selected' : ''}`}
                >
                  <svg
                    className="dropdown-item-icon"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    style={{ opacity: isVisible ? 1 : 0 }}
                  >
                    <path d="M20 6 9 17l-5-5" />
                  </svg>
                  <span>
                    {column.charAt(0).toUpperCase() + column.slice(1).replace(/([A-Z])/g, ' $1')}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <table>
        <thead>
          <tr>
            {visibleColumns.name && (
              <th
                onClick={() => handleSort('name')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
                title="Click to sort by Name"
              >
                NAME
                {sortField === 'name' && (
                  <span style={{ marginLeft: '4px' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
            )}
            {visibleColumns.age && (
              <th
                onClick={() => handleSort('age')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
                title="Click to sort by Age"
              >
                AGE
                {sortField === 'age' && (
                  <span style={{ marginLeft: '4px' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
            )}
            {visibleColumns.gender && (
              <th
                onClick={() => handleSort('gender')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
                title="Click to sort by Gender"
              >
                GENDER
                {sortField === 'gender' && (
                  <span style={{ marginLeft: '4px' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
            )}
            {visibleColumns.epic && (
              <th
                onClick={() => handleSort('epic')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
                title="Click to sort by EPIC Number"
              >
                EPIC NUMBER
                {sortField === 'epic' && (
                  <span style={{ marginLeft: '4px' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
            )}
            {visibleColumns.pollingStation && (
              <th
                onClick={() => handleSort('pollingStation')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
                title="Click to sort by Polling Station"
              >
                POLLING STATION
                {sortField === 'pollingStation' && (
                  <span style={{ marginLeft: '4px' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
            )}
            {visibleColumns.affiliation && (
              <th
                onClick={() => handleSort('affiliation')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
                title="Click to sort by Affiliation"
              >
                AFFILIATION
                {sortField === 'affiliation' && (
                  <span style={{ marginLeft: '4px' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
            )}
            {visibleColumns.payee && (
              <th
                onClick={() => handleSort('payee')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
                title="Click to sort by Payee Amount"
              >
                PAYEE
                {sortField === 'payee' && (
                  <span style={{ marginLeft: '4px' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
            )}
            {visibleColumns.status && (
              <th
                onClick={() => handleSort('status')}
                style={{ cursor: 'pointer', userSelect: 'none' }}
                title="Click to sort by Status"
              >
                STATUS
                {sortField === 'status' && (
                  <span style={{ marginLeft: '4px' }}>
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
            )}
          </tr>
        </thead>
        <tbody>
          {currentVoters.length > 0 ? (
            currentVoters.map(voter => (
              <tr
                key={voter.id}
                onClick={() => handleVoterClick(voter)}
                style={{ cursor: 'pointer' }}
              >
                {visibleColumns.name && <td className="name-cell">{voter.name}</td>}
                {visibleColumns.age && <td>{voter.age}</td>}
                {visibleColumns.gender && (
                  <td>
                    <span className={`gender-badge ${voter.gender.toLowerCase()}`}>
                      {voter.gender}
                    </span>
                  </td>
                )}
                {visibleColumns.epic && (
                  <td>
                    <span className="epic-code">{voter.epic}</span>
                  </td>
                )}
                {visibleColumns.pollingStation && <td>{voter.pollingStation}</td>}
                {visibleColumns.affiliation && (
                  <td>
                    {voter.supporter_status ? (
                      <span className={`affiliation-badge ${voter.supporter_status.toLowerCase().replace(' ', '-')}`}>
                        {voter.supporter_status}
                      </span>
                    ) : (
                      <span className="affiliation-badge unknown">Unknown</span>
                    )}
                  </td>
                )}
                {visibleColumns.payee && (
                  <td>
                    {(() => {
                      const transactionData = getVoterTransactionData(voter.id);
                      return transactionData.totalAmount > 0 ? (
                        <span className="payee-amount" title={`${transactionData.transactionCount} transactions`}>
                          {transactionData.formattedAmount}
                        </span>
                      ) : (
                        <span className="payee-none">-</span>
                      );
                    })()}
                  </td>
                )}
                {visibleColumns.status && (
                  <td>
                    <span
                      className={`status-indicator status-${voter.status?.toLowerCase() || 'active'}`}
                    ></span>
                    {voter.status || 'Active'}
                  </td>
                )}
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={Object.values(visibleColumns).filter(Boolean).length}
                className="empty-state"
              >
                <div className="empty-state-content">
                  <svg
                    width="48"
                    height="48"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                  >
                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                    <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                  </svg>
                  <h3>No voters found</h3>
                  <p>Try adjusting your search or filter criteria</p>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
      <div className="table-footer">
        <div className="table-info">
          {sortedVoters.length > 0 ? (
            <>
              Showing {startIndex + 1}-{Math.min(endIndex, sortedVoters.length)} of{' '}
              {sortedVoters.length} entries
              {sortedVoters.length !== voters.length && ` (filtered from ${voters.length} total)`}
            </>
          ) : (
            'No entries to show'
          )}
        </div>
        {totalPages > 1 && (
          <div className="pagination">
            <button
              className="page-btn"
              disabled={currentPage === 1}
              onClick={handlePreviousPage}
              aria-label="Previous page"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m15 18-6-6 6-6" />
              </svg>
            </button>

            {getPageNumbers().map(pageNum => (
              <button
                key={pageNum}
                className={`page-btn ${pageNum === currentPage ? 'is-active' : ''}`}
                onClick={() => handlePageChange(pageNum)}
                aria-current={pageNum === currentPage ? 'page' : undefined}
              >
                {pageNum}
              </button>
            ))}

            <button
              className="page-btn"
              disabled={currentPage === totalPages}
              onClick={handleNextPage}
              aria-label="Next page"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default DataTable;
