import { useState, useRef, useEffect } from 'react';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import { useLightSweep } from '../hooks/useLightSweep';
import { useDialogContext } from './DialogProvider';
import SettingsService from '../database/SettingsService';
import { AuthService, User } from '../services/AuthService';

function SettingsDashboard() {
  const { state, dispatch } = useApp();
  const { isAdmin } = useAuth();
  const { isSettingsDashboardOpen } = state;
  const { showConfirm, showSuccess, showError } = useDialogContext();
  const containerRef = useRef<HTMLDivElement>(null);
  const triggerLightSweep = useLightSweep();
  const [selectedCategory, setSelectedCategory] = useState<string>('community');
  const [newItemInput, setNewItemInput] = useState('');
  const [categoryData, setCategoryData] = useState<{ [key: string]: string[] }>({
    community: [],
    religion: [],
    economic_status: [],
    education: [],
    occupation: [],
    transaction_purpose: [],
  });
  const [settingsService] = useState(() => new SettingsService());

  // User management state
  const [users, setUsers] = useState<User[]>([]);
  const [newUsername, setNewUsername] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [editUsername, setEditUsername] = useState('');
  const [editPassword, setEditPassword] = useState('');
  const [authService] = useState(() => new AuthService());

  const handleClose = () => {
    dispatch({ type: 'TOGGLE_SETTINGS_DASHBOARD' });
  };

  // Load category data from database
  const loadCategoryData = async () => {
    try {
      const [community, religion, economic_status, education, occupation, transaction_purpose] = await Promise.all([
        settingsService.getCategorySettings('community'),
        settingsService.getCategorySettings('religion'),
        settingsService.getCategorySettings('economic_status'),
        settingsService.getCategorySettings('education'),
        settingsService.getCategorySettings('occupation'),
        settingsService.getCategorySettings('transaction_purpose'),
      ]);

      setCategoryData({
        community,
        religion,
        economic_status,
        education,
        occupation,
        transaction_purpose,
      });
    } catch (error) {
      console.error('Failed to load category data:', error);
    }
  };

  // Load data when component mounts or panel opens
  useEffect(() => {
    if (isSettingsDashboardOpen) {
      loadCategoryData();
    }
  }, [isSettingsDashboardOpen]);

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  const handleAddItem = async () => {
    const trimmedItem = newItemInput.trim();
    if (trimmedItem) {
      try {
        const success = await settingsService.addCategorySetting(selectedCategory, trimmedItem);
        if (success) {
          setNewItemInput('');
          // Reload the category data
          await loadCategoryData();
        } else {
          showConfirm({
            title: 'Add Item Failed',
            message: 'Failed to add item. It may already exist.',
            variant: 'default',
            confirmText: 'OK',
            onConfirm: () => {},
          });
        }
      } catch (error) {
        console.error('Failed to add item:', error);
        showConfirm({
          title: 'Error',
          message: 'Failed to add item. Please try again.',
          variant: 'default',
          confirmText: 'OK',
          onConfirm: () => {},
        });
      }
    }
  };

  const handleRemoveItem = async (item: string) => {
    try {
      const success = await settingsService.removeCategorySetting(selectedCategory, item);
      if (success) {
        // Reload the category data
        await loadCategoryData();
      } else {
        showConfirm({
          title: 'Remove Item Failed',
          message: 'Failed to remove item.',
          variant: 'default',
          confirmText: 'OK',
          onConfirm: () => {},
        });
      }
    } catch (error) {
      console.error('Failed to remove item:', error);
      showConfirm({
        title: 'Error',
        message: 'Failed to remove item. Please try again.',
        variant: 'default',
        confirmText: 'OK',
        onConfirm: () => {},
      });
    }
  };

  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddItem();
    }
  };

  const handleEditItem = (item: string) => {
    setEditingItem(item);
    setEditValue(item);
  };

  const handleSaveEdit = async () => {
    if (!editingItem || !editValue.trim()) return;

    try {
      const success = await settingsService.updateCategorySetting(
        selectedCategory,
        editingItem,
        editValue.trim()
      );
      if (success) {
        setEditingItem(null);
        setEditValue('');
        await loadCategoryData();
      } else {
        showConfirm({
          title: 'Update Item Failed',
          message: 'Failed to update item. It may already exist.',
          variant: 'default',
          confirmText: 'OK',
          onConfirm: () => {},
        });
      }
    } catch (error) {
      console.error('Failed to update item:', error);
      showConfirm({
        title: 'Error',
        message: 'Failed to update item. Please try again.',
        variant: 'default',
        confirmText: 'OK',
        onConfirm: () => {},
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingItem(null);
    setEditValue('');
  };

  const showDeleteConfirmation = (item: string) => {
    showConfirm({
      title: 'Delete Category Item',
      message: `Are you sure you want to delete "${item}" from ${formatCategoryName(selectedCategory)}?\n\nThis action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: () => {
        handleRemoveItem(item);
      },
    });
  };

  const formatCategoryName = (category: string) => {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ');
  };

  // User management functions
  const loadUsers = async () => {
    if (isAdmin()) {
      const userList = await authService.getAllUsers();
      setUsers(userList);
    }
  };

  const handleAddUser = async () => {
    if (!newUsername.trim() || !newPassword.trim()) {
      showError('Validation Error', 'Please enter username and password');
      return;
    }

    if (newPassword.length < 6) {
      showError('Validation Error', 'Password must be at least 6 characters long');
      return;
    }

    const success = await authService.createUser(newUsername.trim(), newPassword);
    if (success) {
      setNewUsername('');
      setNewPassword('');
      await loadUsers(); // Refresh user list
      showSuccess('User Created', 'User created successfully');
    } else {
      showError('Creation Failed', 'Failed to create user. Username may already exist.');
    }
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setEditUsername(user.username);
    setEditPassword('');
  };

  const handleSaveUserEdit = async () => {
    if (!editingUser) return;

    if (!editUsername.trim()) {
      showError('Validation Error', 'Please enter username');
      return;
    }

    if (editPassword && editPassword.length < 6) {
      showError('Validation Error', 'Password must be at least 6 characters long');
      return;
    }

    // For now, we'll only support password changes since username changes are more complex
    if (editPassword) {
      const success = await authService.createUser(editUsername.trim(), editPassword, editingUser.role);
      if (success) {
        // Delete old user if username changed
        if (editUsername !== editingUser.username) {
          await authService.deleteUser(editingUser.id);
        }
        setEditingUser(null);
        setEditUsername('');
        setEditPassword('');
        await loadUsers();
        showSuccess('User Updated', 'User updated successfully');
      } else {
        showError('Update Failed', 'Failed to update user');
      }
    } else {
      showError('Validation Error', 'Please enter a new password to update the user');
    }
  };

  const handleCancelUserEdit = () => {
    setEditingUser(null);
    setEditUsername('');
    setEditPassword('');
  };

  const handleDeleteUser = async (userId: number, username: string) => {
    if (username === 'admin') {
      showError('Cannot Delete', 'Cannot delete admin user');
      return;
    }

    showConfirm({
      title: 'Delete User',
      message: `Are you sure you want to delete user "${username}"?\n\nThis action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: async () => {
        const success = await authService.deleteUser(userId);
        if (success) {
          await loadUsers(); // Refresh user list
          showSuccess('User Deleted', 'User deleted successfully');
        } else {
          showError('Delete Failed', 'Failed to delete user');
        }
      },
    });
  };

  const handlePromoteUser = async (userId: number, username: string) => {
    showConfirm({
      title: 'Promote User',
      message: `Are you sure you want to promote "${username}" to an admin?\n\nThis will grant them full access to the system.`,
      variant: 'danger',
      confirmText: 'Promote',
      cancelText: 'Cancel',
      onConfirm: async () => {
        const success = await authService.promoteUser(userId);
        if (success) {
          await loadUsers();
          showSuccess('User Promoted', `User "${username}" has been promoted to admin.`);
        } else {
          showError('Promotion Failed', 'Failed to promote user.');
        }
      },
    });
  };

  // Trigger light sweep when panel opens and load data
  useEffect(() => {
    if (isSettingsDashboardOpen) {
      triggerLightSweep(containerRef.current);
      loadCategoryData();
      if (isAdmin()) {
        loadUsers();
      }
    }
  }, [isSettingsDashboardOpen, triggerLightSweep]);

  return (
    <div
      ref={containerRef}
      className={`settings-dashboard-container ${isSettingsDashboardOpen ? 'is-open' : ''}`}
    >
      <div className="settings-dashboard">
        <div className="filter-header">
          <div className="filter-title">
            <svg
              className="dropdown-item-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M4 7V4h16v3" />
              <path d="M9 20h6" />
              <path d="M12 4v16" />
            </svg>
            Manage Data
          </div>
          <button onClick={handleClose} className="panel-close" aria-label="Close panel">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div className="settings-controls">
          <div className="filter-group">
            <label htmlFor="category-select">Category</label>
            <div className="select-wrapper">
              <select id="category-select" value={selectedCategory} onChange={handleCategoryChange}>
                <option value="community">Community</option>
                <option value="religion">Religion</option>
                <option value="economic_status">Economic Status</option>
                <option value="transaction_purpose">Transaction Purpose</option>
                {isAdmin() && <option value="user_management">User Management</option>}
              </select>
            </div>
          </div>
          {selectedCategory !== 'user_management' && (
            <div className="add-item-form">
              <input
                type="text"
                id="new-item-input"
                placeholder="Enter new item"
                value={newItemInput}
                onChange={e => setNewItemInput(e.target.value)}
                onKeyDown={handleKeyDown}
              />
              <button onClick={handleAddItem} className="btn btn-primary">
                Add
              </button>
            </div>
          )}
        </div>

        {/* Category Management */}
        {selectedCategory !== 'user_management' && (
          <div className="data-list">
            <ul id="item-list">
              {categoryData[selectedCategory]?.map((item, index) => (
                <li key={index}>
                  {editingItem === item ? (
                    <>
                      <input
                        type="text"
                        value={editValue}
                        onChange={e => setEditValue(e.target.value)}
                        className="edit-input"
                        autoFocus
                      />
                      <div className="item-actions">
                        <button onClick={handleSaveEdit} className="save-btn">
                          Save
                        </button>
                        <button onClick={handleCancelEdit} className="cancel-btn">
                          Cancel
                        </button>
                      </div>
                    </>
                  ) : (
                    <>
                      <span className="item-text">{item}</span>
                      <div className="item-actions">
                        <button onClick={() => handleEditItem(item)} className="edit-btn">
                          Edit
                        </button>
                        <button onClick={() => showDeleteConfirmation(item)} className="delete-btn">
                          Delete
                        </button>
                      </div>
                    </>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* User Management */}
        {selectedCategory === 'user_management' && isAdmin() && (
          <div className="user-management">
            <div className="add-item-form">
              <div className="filter-group">
                <label htmlFor="username-input">Username</label>
                <input
                  id="username-input"
                  type="text"
                  placeholder="Username"
                  value={newUsername}
                  onChange={(e) => setNewUsername(e.target.value)}
                />
              </div>
              <div className="filter-group">
                <label htmlFor="password-input">Password</label>
                <input
                  id="password-input"
                  type="password"
                  placeholder="Password (min 6 characters)"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  minLength={6}
                />
              </div>
              <button onClick={handleAddUser} className="btn btn-primary">
                Add User
              </button>
            </div>

            <div className="data-list">
              <ul id="user-list">
                {users.map(user => (
                  <li key={user.id}>
                    {editingUser?.id === user.id ? (
                      <>
                        <input
                          type="text"
                          value={editUsername}
                          onChange={e => setEditUsername(e.target.value)}
                          className="edit-input"
                          placeholder="Username"
                        />
                        <input
                          type="password"
                          value={editPassword}
                          onChange={e => setEditPassword(e.target.value)}
                          className="edit-input"
                          placeholder="New password (min 6 characters)"
                          minLength={6}
                        />
                        <div className="item-actions">
                          <button onClick={handleSaveUserEdit} className="save-btn">
                            Save
                          </button>
                          <button onClick={handleCancelUserEdit} className="cancel-btn">
                            Cancel
                          </button>
                        </div>
                      </>
                    ) : (
                      <>
                        <span className="item-text">
                          {user.username} ({user.role})
                        </span>
                        <div className="item-actions">
                          {user.username !== 'admin' && (
                            <>
                              <button onClick={() => handleEditUser(user)} className="edit-btn">
                                Edit
                              </button>
                              <button onClick={() => handleDeleteUser(user.id, user.username)} className="delete-btn">
                                Delete
                              </button>
                              {user.role !== 'admin' && (
                                <button
                                  onClick={() => handlePromoteUser(user.id, user.username)}
                                  className="promote-btn"
                                >
                                  Promote to Admin
                                </button>
                              )}
                            </>
                          )}
                        </div>
                      </>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default SettingsDashboard;
