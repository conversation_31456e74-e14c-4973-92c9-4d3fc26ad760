import React from 'react';
import { useAuth } from '../context/AuthContext';
import ReportsIcon from './icons/ReportsIcon';
import SettingsIcon from './icons/SettingsIcon';
import ManageDataIcon from './icons/ManageDataIcon';
import ImportCsvIcon from './icons/ImportCsvIcon';
import ExportPdfIcon from './icons/ExportPdfIcon';
import LogoutIcon from './icons/LogoutIcon';

interface SidebarFooterProps {
  totalStations: number;
  selectedStations: number;
  selectedSections: number;
  onReportsClick: (e: React.MouseEvent) => void;
  onSettingsClick: (e: React.MouseEvent) => void;
  onManageDataClick: () => void;
  onImportCSVClick: () => void;
  onExportCSVClick: () => void;
  onExportPDFClick: () => void;
  onBackupDatabaseClick: () => void;
  onRestoreDatabaseClick: () => void;
  isSettingsOpen: boolean;
  dropdownRef: React.RefObject<HTMLDivElement>;
}

const SidebarFooter: React.FC<SidebarFooterProps> = ({
  totalStations,
  selectedStations,
  selectedSections,
  onReportsClick,
  onSettingsClick,
  onManageDataClick,
  onImportCSVClick,
  onExportCSVClick,
  onExportPDFClick,
  onBackupDatabaseClick,
  onRestoreDatabaseClick,
  isSettingsOpen,
  dropdownRef,
}) => {
  const { canAccessReports, canAccessSettings, logout } = useAuth();

  return (
    <>
      <footer className="sidebar-footer">
        <section className="sidebar-section">
          <div className="sidebar-title">Management</div>
          {canAccessReports() && (
            <a href="#" onClick={onReportsClick} className="sidebar-item">
              <span className="icon" aria-hidden="true">
                <ReportsIcon />
              </span>
              <span>Reports</span>
            </a>
          )}
          <div className="dropdown-container" ref={dropdownRef}>
            <a href="#" onClick={onSettingsClick} className="sidebar-item">
              <span className="icon" aria-hidden="true">
                <SettingsIcon />
              </span>
              <span>Settings</span>
            </a>
            <div className={`dropdown-menu dropdown-menu-up ${isSettingsOpen ? 'is-open' : ''}`}>
              {canAccessSettings() && (
                <>
                  <div onClick={onBackupDatabaseClick} className="dropdown-item">
                    <svg
                      className="dropdown-item-icon"
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                      <path d="M8 14h.01"></path>
                      <path d="M12 14h.01"></path>
                      <path d="M16 14h.01"></path>
                    </svg>
                    <span>Backup Database</span>
                  </div>
                  <div onClick={onRestoreDatabaseClick} className="dropdown-item">
                    <svg
                      className="dropdown-item-icon"
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                      <path d="M21 3v5h-5"></path>
                      <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                      <path d="M8 16H3v5"></path>
                    </svg>
                    <span>Restore Database</span>
                  </div>
                  <div onClick={onManageDataClick} className="dropdown-item">
                    <ManageDataIcon />
                    <span>Manage Data</span>
                  </div>
                  <div onClick={onImportCSVClick} className="dropdown-item">
                    <ImportCsvIcon />
                    <span>Import CSV</span>
                  </div>
                  <div onClick={onExportCSVClick} className="dropdown-item">
                    <svg
                      className="dropdown-item-icon"
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                      <polyline points="14,2 14,8 20,8"></polyline>
                      <line x1="16" y1="13" x2="8" y2="13"></line>
                      <line x1="16" y1="17" x2="8" y2="17"></line>
                      <polyline points="10,9 9,9 8,9"></polyline>
                    </svg>
                    <span>Export CSV</span>
                  </div>
                  <div onClick={onExportPDFClick} className="dropdown-item">
                    <ExportPdfIcon />
                    <span>Export PDF</span>
                  </div>
                </>
              )}
              <div onClick={logout} className="dropdown-item">
                <LogoutIcon />
                <span>Logout</span>
              </div>
            </div>
          </div>
        </section>
      </footer>
      <div className="footer-summary">
        <small>
          {selectedStations}/{totalStations} PS Selected
          {selectedSections > 0 && <> • {selectedSections} Sections</>}
        </small>
      </div>
    </>
  );
};

export default SidebarFooter;
