import { useState } from 'react';
import { useApp } from '../context/AppContext';
import { useClickOutside } from '../hooks/useClickOutside';
import { usePollingStations } from '../hooks/usePollingStations';
import { useExport } from '../hooks/useExport';
import { useDialogContext } from './DialogProvider';
import PollingStationList from './PollingStationList';
import SidebarFooter from './SidebarFooter';
import JSZip from 'jszip';
import { showSuccess, showError } from '../utils/notifications';
import { TauriExportService } from '../services/TauriExportService';

function Sidebar() {
  const { state, dispatch, database } = useApp();
  const { voters } = state;
  const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);
  const { exportToPDF, exportToCSV } = useExport();
  const { showConfirm, showSuccess, showError } = useDialogContext();

  const {
    pollingStations,
    toggleStationOpen,
    toggleStationSelected,
    toggleSectionSelected,
    getSelectedCount,
    getSelectedSectionsCount,
    totalStations,
    toggleAll,
  } = usePollingStations(voters, dispatch);

  // Check if all stations are selected
  const areAllStationsSelected = pollingStations.every(station => station.isSelected);

  const dropdownRef = useClickOutside<HTMLDivElement>(() => {
    if (hasInteracted) {
      setIsSettingsDropdownOpen(false);
    }
  });

  const handleReportsClick = (e: React.MouseEvent) => {
    e.preventDefault();
    dispatch({ type: 'SET_CURRENT_VIEW', payload: 'reports' });
  };

  const handleSettingsClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setHasInteracted(true);
    setIsSettingsDropdownOpen(!isSettingsDropdownOpen);
  };

  const handleManageDataClick = () => {
    dispatch({ type: 'TOGGLE_SETTINGS_DASHBOARD' });
    setIsSettingsDropdownOpen(false);
  };

  const handleImportCSVClick = () => {
    // Create a file input element
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv';
    input.multiple = true; // Allow multiple file selection
    input.onchange = async e => {
      const files = Array.from((e.target as HTMLInputElement).files || []);

      // Sort files by name with proper numeric ordering
      files.sort((a, b) => {
        // Extract numbers from filename for proper numeric sorting
        const getNumericParts = (filename: string) => {
          return filename.replace(/\D/g, '').padStart(10, '0') + filename;
        };
        return getNumericParts(a.name).localeCompare(getNumericParts(b.name));
      });

      if (files.length > 0) {
        try {
          let totalImported = 0;
          let totalUpdated = 0;
          let totalSkipped = 0;
          let totalErrors: string[] = [];

          // Show progress
          dispatch({
            type: 'SET_IMPORT_PROGRESS',
            payload: `Processing ${files.length} file(s)...`,
          });

          for (let i = 0; i < files.length; i++) {
            const file = files[i];
            dispatch({
              type: 'SET_IMPORT_PROGRESS',
              payload: `Processing ${file.name} (${i + 1}/${files.length})...`,
            });
            console.log(`📁 Processing file ${i + 1}/${files.length}: ${file.name}`);

            try {
              const result = await database.importCSV(file);
              totalImported += result.importedRows;
              totalUpdated += result.updatedRows || 0;
              totalSkipped += result.skippedRows;
              totalErrors.push(...result.errors);
            } catch (error) {
              totalErrors.push(
                `${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
              );
            }
          }

          // Clear progress
          dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });

          // Force refresh the dashboard by reloading voters
          await database.loadVoters();

          const summary = `Import completed!\nFiles processed: ${files.length}\nNew records: ${totalImported}\nUpdated records: ${totalUpdated}\nSkipped: ${totalSkipped}${totalErrors.length > 0 ? `\nErrors: ${totalErrors.length}` : ''}`;
          showSuccess('Import Complete', summary);

          if (totalErrors.length > 0) {
            console.error('Import errors:', totalErrors);
          }
        } catch (error) {
          showError(
            'Import Failed',
            `Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
        }
      }
    };
    input.click();
    setIsSettingsDropdownOpen(false);
  };

  const handleExportCSVClick = () => {
    try {
      setIsSettingsDropdownOpen(false);
      dispatch({ type: 'SET_IMPORT_PROGRESS', payload: 'Exporting CSV...' });

      // Add a small delay to show progress, then export
      setTimeout(() => {
        try {
          exportToCSV();
          dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });
          showSuccess('Export Complete', 'CSV file has been downloaded successfully');
        } catch (error) {
          dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });
          showError('Export Failed', error instanceof Error ? error.message : 'Export failed');
        }
      }, 100);
    } catch (error) {
      dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });
      showError('Export Failed', error instanceof Error ? error.message : 'Export failed');
    }
  };

  const handleExportPDFClick = () => {
    try {
      setIsSettingsDropdownOpen(false);
      dispatch({ type: 'SET_IMPORT_PROGRESS', payload: 'Generating PDF...' });

      // Add a small delay to show progress, then export
      setTimeout(() => {
        try {
          exportToPDF();
          dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });
          showSuccess('Export Complete', 'PDF file has been downloaded successfully');
        } catch (error) {
          dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });
          showError('PDF Export Failed', error instanceof Error ? error.message : 'PDF export failed');
        }
      }, 100);
    } catch (error) {
      dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });
      showError('PDF Export Failed', error instanceof Error ? error.message : 'PDF export failed');
    }
  };

  const handleBackupDatabaseClick = async () => {
    try {
      dispatch({ type: 'SET_IMPORT_PROGRESS', payload: 'Creating backup...' });

      // Get the database data
      const dbData = await database.exportDatabase();

      // Check if running in Tauri (v2 uses __TAURI_INTERNALS__)
      const isTauri = typeof window !== 'undefined' && (
        (window as any).__TAURI_INTERNALS__ ||
        (window as any).__TAURI__
      );

      if (isTauri) {
        // Use Tauri file system for backup
        const dateStr = new Date().toISOString().split('T')[0];
        const filename = `electixir_backup_${dateStr}.json`;

        await TauriExportService.exportDatabaseBackup(dbData, filename);

        dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });
        showSuccess('Backup Complete', `Database backup saved as: ${filename}`);
      } else {
        // Browser fallback - create ZIP file
        const zip = new JSZip();
        const dateStr = new Date().toISOString().split('T')[0];
        const dbFileName = `electixir_backup_${dateStr}.json`;

        // Add the database data to ZIP
        zip.file(dbFileName, dbData);

        // Generate ZIP file
        const zipBlob = await zip.generateAsync({ type: 'blob' });

        // Download the ZIP file
        const url = URL.createObjectURL(zipBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `electixir_backup_${dateStr}.zip`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });
        showSuccess('Backup Complete', 'Database backup has been downloaded successfully');
      }
    } catch (error) {
      dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });
      showError(
        'Backup Failed',
        `Backup failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
    setIsSettingsDropdownOpen(false);
  };

  const handleRestoreDatabaseClick = () => {
    const message =
      'This will replace ALL your current data with the backup file.\n\nThis action cannot be undone and will overwrite:\n• All voter records\n• All transactions\n• All settings\n• All polling stations';

    showConfirm({
      title: 'Restore Database',
      message,
      variant: 'danger',
      confirmText: 'Restore',
      cancelText: 'Cancel',
      onConfirm: () => {
        performRestore();
      },
    });
    setIsSettingsDropdownOpen(false);
  };

  const performRestore = () => {
    // Create a file input element
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.zip,.sqlite,.db';
    input.onchange = async e => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          dispatch({ type: 'SET_IMPORT_PROGRESS', payload: 'Processing backup file...' });

          let dbData: Uint8Array;

          if (file.name.toLowerCase().endsWith('.zip')) {
            // Handle ZIP file
            dispatch({ type: 'SET_IMPORT_PROGRESS', payload: 'Extracting ZIP file...' });

            const zip = new JSZip();
            const zipContent = await zip.loadAsync(file);

            // Find the database file in the ZIP (look for .json or .db files)
            const dbFiles = Object.keys(zipContent.files).filter(
              name => name.toLowerCase().endsWith('.json') || name.toLowerCase().endsWith('.db')
            );

            if (dbFiles.length === 0) {
              throw new Error('No database file found in ZIP archive');
            }

            if (dbFiles.length > 1) {
              throw new Error(
                'Multiple database files found in ZIP. Please use a backup with only one database file.'
              );
            }

            // Extract the database file
            const dbFile = zipContent.files[dbFiles[0]];
            const arrayBuffer = await dbFile.async('arraybuffer');
            dbData = new Uint8Array(arrayBuffer);
          } else {
            // Handle direct database file
            const arrayBuffer = await file.arrayBuffer();
            dbData = new Uint8Array(arrayBuffer);
          }

          // Import the database
          dispatch({ type: 'SET_IMPORT_PROGRESS', payload: 'Restoring database...' });
          await database.importDatabase(dbData);

          // Clear progress
          dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });

          showSuccess(
            'Restore Complete',
            'Database restored successfully!\n\nThe page will reload to reflect the changes.'
          );

          // Reload the page after a short delay to let user see the success message
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } catch (error) {
          dispatch({ type: 'SET_IMPORT_PROGRESS', payload: null });
          showError(
            'Restore Failed',
            `Restore failed: ${error instanceof Error ? error.message : 'Unknown error'}\n\nPlease ensure you selected a valid backup file.`
          );
        }
      }
    };
    input.click();
    setIsSettingsDropdownOpen(false);
  };

  const handleToggleAllStations = () => {
    toggleAll(!areAllStationsSelected);
  };

  return (
    <aside className="sidebar" aria-label="Main navigation">
      <header className="sidebar-header">
        <div className="sidebar-title">Polling Stations</div>
        <button
          className="toggle-all-btn"
          onClick={handleToggleAllStations}
          title="Select/Deselect all polling stations"
        >
          {areAllStationsSelected ? 'None' : 'All'}
        </button>
      </header>
      <nav className="sidebar-content">
        <PollingStationList
          pollingStations={pollingStations}
          onToggle={(e, stationId) => {
            e.preventDefault();
            toggleStationOpen(stationId);
          }}
          onStationSelect={toggleStationSelected}
          onSectionSelect={toggleSectionSelected}
        />
      </nav>
      <SidebarFooter
        totalStations={totalStations}
        selectedStations={getSelectedCount()}
        selectedSections={getSelectedSectionsCount()}
        onReportsClick={handleReportsClick}
        onSettingsClick={handleSettingsClick}
        onManageDataClick={handleManageDataClick}
        onImportCSVClick={handleImportCSVClick}
        onExportCSVClick={handleExportCSVClick}
        onExportPDFClick={handleExportPDFClick}
        onBackupDatabaseClick={handleBackupDatabaseClick}
        onRestoreDatabaseClick={handleRestoreDatabaseClick}
        isSettingsOpen={isSettingsDropdownOpen}
        dropdownRef={dropdownRef}
      />
    </aside>
  );
}

export default Sidebar;
