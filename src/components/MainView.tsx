import StatsCards from './StatsCards';
import FilterPanel from './FilterPanel';
import DataTable from './DataTable';
import SettingsDashboard from './SettingsDashboard';
import Toolbar from './Toolbar';
import ImportProgress from './ImportProgress';

interface MainViewProps {
  onFilterToggle: () => void;
  onAddVoter: () => void;
  isFilterPanelOpen: boolean;
  importProgress?: string | null;
}

function MainView({
  onFilterToggle,
  onAddVoter,
  isFilterPanelOpen,
  importProgress,
}: MainViewProps) {
  return (
    <div id="main-view">
      <Toolbar
        onFilterToggle={onFilterToggle}
        onAddVoter={onAddVoter}
        isFilterPanelOpen={isFilterPanelOpen}
      />

      {/* Import Progress Indicator */}
      {importProgress && <ImportProgress message={importProgress} />}

      <StatsCards />

      <FilterPanel />

      <SettingsDashboard />

      <DataTable />
    </div>
  );
}

export default MainView;
