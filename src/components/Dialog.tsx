import React, { useEffect, useRef } from 'react';

interface BaseDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

interface ConfirmDialogProps extends BaseDialogProps {
  onConfirm: () => void;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'danger';
}

interface AlertDialogProps extends BaseDialogProps {
  variant?: 'success' | 'error' | 'info';
}

// Base Dialog Component
function BaseDialog({ isOpen, onClose, title, children }: BaseDialogProps) {
  const dialogRef = useRef<HTMLDialogElement>(null);

  useEffect(() => {
    const dialog = dialogRef.current;
    if (!dialog) return;

    if (isOpen) {
      dialog.showModal();
    } else {
      dialog.close();
    }
  }, [isOpen]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === dialogRef.current) {
      onClose();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <dialog
      ref={dialogRef}
      className="custom-dialog"
      onClick={handleBackdropClick}
      onKeyDown={handleKeyDown}
    >
      <div className="dialog-content">
        <div className="dialog-header">
          <h3 className="dialog-title">{title}</h3>
          <button onClick={onClose} className="dialog-close" aria-label="Close dialog">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div className="dialog-body">{children}</div>
      </div>
    </dialog>
  );
}

// Confirm Dialog
export function ConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  children,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
}: ConfirmDialogProps) {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <BaseDialog isOpen={isOpen} onClose={onClose} title={title}>
      <div className="dialog-message">{children}</div>
      <div className="dialog-actions">
        <button onClick={onClose} className="btn">
          {cancelText}
        </button>
        <button
          onClick={handleConfirm}
          className={`btn ${variant === 'danger' ? 'btn-danger' : 'btn-primary'}`}
        >
          {confirmText}
        </button>
      </div>
    </BaseDialog>
  );
}

// Alert Dialog
export function AlertDialog({
  isOpen,
  onClose,
  title,
  children,
  variant = 'info',
}: AlertDialogProps) {
  return (
    <BaseDialog isOpen={isOpen} onClose={onClose} title={title}>
      <div className={`dialog-message dialog-message-${variant}`}>
        <div className="dialog-icon">
          {variant === 'success' && (
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
          )}
          {variant === 'error' && (
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="15" y1="9" x2="9" y2="15"></line>
              <line x1="9" y1="9" x2="15" y2="15"></line>
            </svg>
          )}
          {variant === 'info' && (
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="16" x2="12" y2="12"></line>
              <line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>
          )}
        </div>
        <div className="dialog-text">{children}</div>
      </div>
      <div className="dialog-actions">
        <button onClick={onClose} className="btn btn-primary">
          OK
        </button>
      </div>
    </BaseDialog>
  );
}
