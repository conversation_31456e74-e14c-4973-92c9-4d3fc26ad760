interface ImportProgressProps {
  message: string;
}

function ImportProgress({ message }: ImportProgressProps) {
  return (
    <div className="import-progress">
      <div className="import-progress-content">
        <svg
          className="spinner"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
        >
          <path d="M21 12a9 9 0 11-6.219-8.56" />
        </svg>
        <span>{message}</span>
      </div>
    </div>
  );
}

export default ImportProgress;
