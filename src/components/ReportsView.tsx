import { useState, useEffect } from 'react';
import { useApp } from '../context/AppContext';
import { ReportFilters, ReportingService } from '../services/ReportingService';
import { Transaction } from '../database/TransactionService';
import TransactionService from '../database/TransactionService';
import { ReportExportService } from '../services/ReportExportService';
import PollingStationReport from './reports/PollingStationReport';
import HouseholdReport from './reports/HouseholdReport';
import BeneficiaryReport from './reports/BeneficiaryReport';

interface ReportsViewProps {
  onBackToMain: () => void;
}

type ReportType = 'polling-station' | 'household' | 'beneficiary';

function ReportsView({ onBackToMain }: ReportsViewProps) {
  const { state, database } = useApp();
  const { voters } = state;

  const [activeReport, setActiveReport] = useState<ReportType>('polling-station');
  const [filters, setFilters] = useState<ReportFilters>({});
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [households, setHouseholds] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Load data when component mounts
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        // Load transactions
        const transactionService = new TransactionService();
        const allTransactions = await transactionService.getAllTransactions();
        setTransactions(allTransactions);

        // Load households
        const householdData = await database.getHouseholds();
        setHouseholds(householdData);
      } catch (error) {
        console.error('Failed to load report data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [database]);

  const handleExportReport = async () => {
    setIsExporting(true);

    try {
      let reportData: any;
      let reportTitle: string;

      // Generate report data based on active report type
      switch (activeReport) {
        case 'polling-station':
          reportData = ReportingService.generatePollingStationReport(voters, filters);
          reportTitle = 'Polling Station Report';
          break;
        case 'household':
          reportData = ReportingService.generateHouseholdReport(households, filters);
          reportTitle = 'Family & Household Report';
          break;
        case 'beneficiary':
          reportData = ReportingService.generateBeneficiaryReport(transactions, voters, filters);
          reportTitle = 'Beneficiary Report';
          break;
        default:
          throw new Error('Invalid report type');
      }

      // Export to PDF
      await ReportExportService.exportReportToPDF({
        reportType: activeReport,
        title: reportTitle,
        data: reportData,
        filters,
        includeCharts: true,
      });

    } catch (error) {
      console.error('Export failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      alert(`Failed to export report: ${errorMessage}\n\nPlease try again.`);
    } finally {
      setIsExporting(false);
    }
  };

  const reportTabs = [
    {
      id: 'polling-station' as ReportType,
      label: 'Polling Station',
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
          <polyline points="9 22 9 12 15 12 15 22" />
        </svg>
      ),
    },
    {
      id: 'household' as ReportType,
      label: 'Family & Household',
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
          <circle cx="9" cy="7" r="4" />
          <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
          <path d="M16 3.13a4 4 0 0 1 0 7.75" />
        </svg>
      ),
    },
    {
      id: 'beneficiary' as ReportType,
      label: 'Beneficiary',
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
        </svg>
      ),
    },
  ];

  return (
    <div id="reports-view">
      <div className="toolbar">
        <h1>Reports</h1>
        <div className="toolbar-right">
          <button
            onClick={handleExportReport}
            className="btn btn-primary"
            disabled={isExporting || isLoading}
          >
            {isExporting ? (
              <>
                <div className="loading-spinner-small"></div>
                Exporting...
              </>
            ) : (
              <>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                  <polyline points="7 10 12 15 17 10" />
                  <line x1="12" y1="15" x2="12" y2="3" />
                </svg>
                Export to PDF
              </>
            )}
          </button>

          <button onClick={onBackToMain} className="btn">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="m15 18-6-6 6-6" />
            </svg>
            Back
          </button>
        </div>
      </div>

      <div className="reports-content">
        {/* Report Tabs */}
        <div className="report-tabs">
          {reportTabs.map((tab) => (
            <button
              key={tab.id}
              className={`report-tab ${activeReport === tab.id ? 'active' : ''}`}
              onClick={() => {
                setActiveReport(tab.id);
                setFilters({}); // Reset filters when switching reports
              }}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        {/* Report Content */}
        <div className="report-container">
          {isLoading ? (
            <div className="loading-state">
              <div className="loading-spinner"></div>
              <p>Loading report data...</p>
            </div>
          ) : (
            <>
              {activeReport === 'polling-station' && (
                <PollingStationReport
                  voters={voters}
                  filters={filters}
                  onFiltersChange={setFilters}
                />
              )}

              {activeReport === 'household' && (
                <HouseholdReport
                  households={households}
                  filters={filters}
                  onFiltersChange={setFilters}
                />
              )}

              {activeReport === 'beneficiary' && (
                <BeneficiaryReport
                  transactions={transactions}
                  voters={voters}
                  filters={filters}
                  onFiltersChange={setFilters}
                />
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default ReportsView;
