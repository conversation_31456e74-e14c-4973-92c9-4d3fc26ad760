/**
 * Web Worker for CSV processing to prevent UI blocking
 */

// Import Papa Parse for CSV parsing
// Note: In a real implementation, you'd need to configure the worker to load <PERSON> Parse
// For now, we'll implement a basic CSV parser

interface CSVWorkerMessage {
  type: 'PARSE_CSV' | 'VALIDATE_CSV';
  data: {
    csvContent: string;
    options?: {
      hasHeader?: boolean;
      delimiter?: string;
      skipEmptyLines?: boolean;
    };
  };
}

interface CSVWorkerResponse {
  type: 'PARSE_COMPLETE' | 'PARSE_ERROR' | 'VALIDATE_COMPLETE' | 'VALIDATE_ERROR' | 'PROGRESS';
  data: any;
  error?: string;
}

// Basic CSV parser implementation
function parseCSV(csvContent: string, options: any = {}) {
  const { hasHeader = true, delimiter = ',', skipEmptyLines = true } = options;

  const lines = csvContent.split('\n');
  const result: any[] = [];
  let headers: string[] = [];

  // Process lines
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    if (skipEmptyLines && !line) {
      continue;
    }

    const values = parseCSVLine(line, delimiter);

    if (i === 0 && hasHeader) {
      headers = values;
      continue;
    }

    if (hasHeader) {
      const row: any = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      result.push(row);
    } else {
      result.push(values);
    }

    // Send progress updates for large files
    if (i % 1000 === 0) {
      self.postMessage({
        type: 'PROGRESS',
        data: {
          processed: i,
          total: lines.length,
          percentage: Math.round((i / lines.length) * 100),
        },
      } as CSVWorkerResponse);
    }
  }

  return result;
}

// Parse a single CSV line handling quoted values
function parseCSVLine(line: string, delimiter: string): string[] {
  const result: string[] = [];
  let current = '';
  let inQuotes = false;
  let i = 0;

  while (i < line.length) {
    const char = line[i];
    const nextChar = line[i + 1];

    if (char === '"') {
      if (inQuotes && nextChar === '"') {
        // Escaped quote
        current += '"';
        i += 2;
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
        i++;
      }
    } else if (char === delimiter && !inQuotes) {
      // End of field
      result.push(current.trim());
      current = '';
      i++;
    } else {
      current += char;
      i++;
    }
  }

  // Add the last field
  result.push(current.trim());

  return result;
}

// Validate CSV structure
function validateCSV(csvContent: string, requiredHeaders: string[] = []) {
  const lines = csvContent.split('\n');
  const errors: string[] = [];

  if (lines.length === 0) {
    errors.push('CSV file is empty');
    return { valid: false, errors };
  }

  // Check headers
  const headerLine = lines[0];
  const headers = parseCSVLine(headerLine, ',');

  requiredHeaders.forEach(required => {
    if (!headers.includes(required)) {
      errors.push(`Missing required header: ${required}`);
    }
  });

  // Basic structure validation
  if (lines.length < 2) {
    errors.push('CSV file must contain at least one data row');
  }

  return {
    valid: errors.length === 0,
    errors,
    headers,
    rowCount: lines.length - 1, // Excluding header
  };
}

// Handle messages from main thread
self.onmessage = function (e: MessageEvent<CSVWorkerMessage>) {
  const { type, data } = e.data;

  try {
    switch (type) {
      case 'PARSE_CSV':
        const parseResult = parseCSV(data.csvContent, data.options);
        self.postMessage({
          type: 'PARSE_COMPLETE',
          data: parseResult,
        } as CSVWorkerResponse);
        break;

      case 'VALIDATE_CSV':
        const validationResult = validateCSV(data.csvContent, ['name', 'epic_number']);
        self.postMessage({
          type: 'VALIDATE_COMPLETE',
          data: validationResult,
        } as CSVWorkerResponse);
        break;

      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  } catch (error) {
    self.postMessage({
      type: 'PARSE_ERROR',
      error: error instanceof Error ? error.message : 'Unknown error',
      data: null,
    } as CSVWorkerResponse);
  }
};

// Export types for TypeScript
export type { CSVWorkerMessage, CSVWorkerResponse };
