import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import {
  PollingStationStats,
  HouseholdStats,
  BeneficiaryStats,
  ReportFilters
} from './ReportingService';
import { formatCurrency, formatNumber, formatPercentage } from '../components/charts/ChartConfig';
import { save } from '@tauri-apps/plugin-dialog';
import { writeFile } from '@tauri-apps/plugin-fs';

export interface ReportExportOptions {
  reportType: 'polling-station' | 'household' | 'beneficiary';
  title: string;
  data: PollingStationStats[] | HouseholdStats | BeneficiaryStats;
  filters: ReportFilters;
  includeCharts?: boolean;
}

export class ReportExportService {
  /**
   * Export report to PDF
   */
  public static async exportReportToPDF(options: ReportExportOptions): Promise<void> {
    const { reportType, title, data, filters, includeCharts = true } = options;

    try {
      const doc = new jsPDF('p', 'mm', 'a4');
      const pageWidth = doc.internal.pageSize.getWidth();
      const pageHeight = doc.internal.pageSize.getHeight();
      let yPosition = 20;

      // Add header
      yPosition = this.addHeader(doc, title, yPosition, pageWidth);

      // Add filters summary
      yPosition = this.addFiltersSection(doc, filters, yPosition, pageWidth);

      // Add summary statistics
      yPosition = this.addSummarySection(doc, reportType, data, yPosition, pageWidth);

      // Add charts if requested
      if (includeCharts) {
        yPosition = await this.addChartsSection(doc, yPosition, pageWidth, pageHeight);
      }

      // Add detailed data tables
      yPosition = this.addDataTables(doc, reportType, data, yPosition, pageWidth, pageHeight);

      // Save the PDF
      const filename = `${reportType}_report_${new Date().toISOString().split('T')[0]}.pdf`;

      // Check if we're in Tauri environment
      const isTauri = typeof window !== 'undefined' && (
        (window as any).__TAURI_INTERNALS__ ||
        (window as any).__TAURI__
      );

      try {
        if (isTauri) {
          // Use Tauri save dialog
          const pdfData = doc.output('arraybuffer');
          const uint8Array = new Uint8Array(pdfData);

          const filePath = await save({
            defaultPath: filename,
            filters: [{
              name: 'PDF Files',
              extensions: ['pdf']
            }]
          });

          if (filePath) {
            await writeFile(filePath, uint8Array);
            console.log('✅ Report PDF exported successfully to:', filePath);
          }
        } else {
          // Use browser download
          doc.save(filename);
        }
      } catch (saveError) {
        console.error('PDF save failed:', saveError);
        throw new Error('Failed to save PDF file. Please check your browser settings.');
      }

    } catch (error) {
      console.error('Failed to export report to PDF:', error);
      throw new Error('PDF export failed. Please try again.');
    }
  }

  /**
   * Add header section to PDF
   */
  private static addHeader(doc: jsPDF, title: string, yPosition: number, _pageWidth: number): number {
    // Main title
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text(title, _pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 10;

    // Subtitle with date
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    const dateStr = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    doc.text(`Generated on ${dateStr}`, _pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 15;

    // Add separator line
    doc.setLineWidth(0.5);
    doc.line(20, yPosition, _pageWidth - 20, yPosition);
    yPosition += 10;

    return yPosition;
  }

  /**
   * Add filters section to PDF
   */
  private static addFiltersSection(doc: jsPDF, filters: ReportFilters, yPosition: number, _pageWidth: number): number {
    const activeFilters = Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '');

    if (activeFilters.length === 0) {
      return yPosition;
    }

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Applied Filters:', 20, yPosition);
    yPosition += 8;

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');

    activeFilters.forEach(([key, value]) => {
      const filterLabel = this.formatFilterLabel(key);
      const filterValue = String(value);
      doc.text(`• ${filterLabel}: ${filterValue}`, 25, yPosition);
      yPosition += 5;
    });

    yPosition += 10;
    return yPosition;
  }

  /**
   * Add summary statistics section
   */
  private static addSummarySection(
    doc: jsPDF,
    reportType: string,
    data: any,
    yPosition: number,
    _pageWidth: number
  ): number {
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Summary Statistics:', 20, yPosition);
    yPosition += 10;

    const summaryData = this.getSummaryData(reportType, data);

    // Create summary table
    autoTable(doc, {
      startY: yPosition,
      head: [['Metric', 'Value']],
      body: summaryData,
      theme: 'grid',
      headStyles: { fillColor: [26, 115, 232] },
      margin: { left: 20, right: 20 },
      styles: { fontSize: 10 },
    });

    return (doc as any).lastAutoTable.finalY + 15;
  }

  /**
   * Add charts section (captures charts from DOM)
   */
  private static async addChartsSection(
    doc: jsPDF,
    yPosition: number,
    _pageWidth: number,
    pageHeight: number
  ): Promise<number> {
    try {
      const chartElements = document.querySelectorAll('.chart-container canvas');

      if (chartElements.length === 0) {
        return yPosition;
      }

      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('Charts:', 20, yPosition);
      yPosition += 10;

      for (let i = 0; i < Math.min(chartElements.length, 4); i++) {
        const canvas = chartElements[i] as HTMLCanvasElement;

        if (yPosition > pageHeight - 80) {
          doc.addPage();
          yPosition = 20;
        }

        // Convert canvas to image
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 160;
        const imgHeight = (canvas.height / canvas.width) * imgWidth;

        doc.addImage(imgData, 'PNG', 25, yPosition, imgWidth, imgHeight);
        yPosition += imgHeight + 15;
      }

      return yPosition;
    } catch (error) {
      console.warn('Failed to capture charts:', error);
      return yPosition;
    }
  }

  /**
   * Add data tables section
   */
  private static addDataTables(
    doc: jsPDF,
    reportType: string,
    data: any,
    yPosition: number,
    _pageWidth: number,
    pageHeight: number
  ): number {
    if (yPosition > pageHeight - 50) {
      doc.addPage();
      yPosition = 20;
    }

    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Detailed Data:', 20, yPosition);
    yPosition += 10;

    const tableData = this.getTableData(reportType, data);

    if (tableData.headers.length > 0 && tableData.rows.length > 0) {
      autoTable(doc, {
        startY: yPosition,
        head: [tableData.headers],
        body: tableData.rows,
        theme: 'striped',
        headStyles: { fillColor: [26, 115, 232] },
        margin: { left: 20, right: 20 },
        styles: { fontSize: 8 },
        columnStyles: tableData.columnStyles || {},
      });
    }

    return (doc as any).lastAutoTable.finalY + 10;
  }

  /**
   * Get summary data based on report type
   */
  private static getSummaryData(reportType: string, data: any): string[][] {
    switch (reportType) {
      case 'polling-station':
        if (Array.isArray(data)) {
          const totalVoters = data.reduce((sum, station) => sum + station.totalVoters, 0);
          const totalActive = data.reduce((sum, station) => sum + station.activeVoters, 0);
          const totalSupporters = data.reduce((sum, station) => sum + station.strongSupporters + station.potentialSupporters, 0);

          return [
            ['Total Polling Stations', data.length.toString()],
            ['Total Voters', formatNumber(totalVoters)],
            ['Active Voters', formatNumber(totalActive)],
            ['Total Supporters', formatNumber(totalSupporters)],
            ['Support Rate', formatPercentage(totalSupporters, totalVoters)],
          ];
        }
        break;

      case 'household':
        return [
          ['Total Households', formatNumber(data.totalHouseholds)],
          ['Average Household Size', data.averageHouseholdSize.toString()],
          ['Multi-Supporter Households', formatNumber(data.householdsWithMultipleSupporters)],
          ['Households with Supporters', formatNumber(data.supporterStatusByHousehold.allSupporters + data.supporterStatusByHousehold.mixedSupport)],
        ];

      case 'beneficiary':
        return [
          ['Total Amount Distributed', formatCurrency(data.totalAmount)],
          ['Total Transactions', formatNumber(data.totalTransactions)],
          ['Unique Beneficiaries', formatNumber(data.topBeneficiaries.length)],
          ['Average per Transaction', formatCurrency(data.averageAmount)],
        ];
    }

    return [];
  }

  /**
   * Get table data based on report type
   */
  private static getTableData(reportType: string, data: any): { headers: string[], rows: string[][], columnStyles?: any } {
    switch (reportType) {
      case 'polling-station':
        if (Array.isArray(data)) {
          return {
            headers: ['Station', 'Total', 'Active', 'Male', 'Female', 'Strong Supporters', 'Potential', 'Undecided', 'Opposed'],
            rows: data.map(station => [
              station.stationName,
              formatNumber(station.totalVoters),
              formatNumber(station.activeVoters),
              formatNumber(station.maleVoters),
              formatNumber(station.femaleVoters),
              formatNumber(station.strongSupporters),
              formatNumber(station.potentialSupporters),
              formatNumber(station.undecided),
              formatNumber(station.opposed),
            ]),
          };
        }
        break;

      case 'household':
        return {
          headers: ['Relationship Name', 'House Number', 'Polling Station', 'Members', 'Supporters', 'Support Rate'],
          rows: data.topHouseholds.slice(0, 20).map((household: any) => [
            household.relationshipName,
            household.houseNumber,
            household.pollingStation,
            formatNumber(household.memberCount),
            formatNumber(household.supporterCount),
            formatPercentage(household.supporterCount, household.memberCount),
          ]),
        };

      case 'beneficiary':
        return {
          headers: ['Beneficiary', 'Total Amount', 'Transactions', 'Average Amount', 'Supporter Status'],
          rows: data.topBeneficiaries.slice(0, 20).map((beneficiary: any) => [
            beneficiary.voterName,
            formatCurrency(beneficiary.totalAmount),
            formatNumber(beneficiary.transactionCount),
            formatCurrency(Math.round(beneficiary.totalAmount / beneficiary.transactionCount)),
            beneficiary.supporterStatus || 'Unknown',
          ]),
          columnStyles: {
            1: { halign: 'right' },
            2: { halign: 'right' },
            3: { halign: 'right' },
          },
        };
    }

    return { headers: [], rows: [] };
  }

  /**
   * Format filter labels for display
   */
  private static formatFilterLabel(key: string): string {
    const labelMap: Record<string, string> = {
      polling_station: 'Polling Station',
      section: 'Section',
      gender: 'Gender',
      status: 'Status',
      supporter_status: 'Supporter Status',
      community: 'Community',
      religion: 'Religion',
      ageFrom: 'Age From',
      ageTo: 'Age To',
      dateFrom: 'Date From',
      dateTo: 'Date To',
      amountFrom: 'Amount From',
      amountTo: 'Amount To',
      householdSizeFrom: 'Household Size From',
      householdSizeTo: 'Household Size To',
    };

    return labelMap[key] || key;
  }
}
