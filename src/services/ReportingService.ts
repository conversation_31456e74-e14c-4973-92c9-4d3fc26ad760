import { VoterData, VoterFilters } from '../database/VoterService';
import { Transaction } from '../database/TransactionService';
import { Household } from '../database/VoterService';

export interface PollingStationStats {
  stationName: string;
  totalVoters: number;
  activeVoters: number;
  inactiveVoters: number;
  maleVoters: number;
  femaleVoters: number;
  strongSupporters: number;
  potentialSupporters: number;
  undecided: number;
  opposed: number;
  ageGroups: {
    '18-25': number;
    '26-35': number;
    '36-45': number;
    '46-55': number;
    '56-65': number;
    '65+': number;
  };
  communities: Record<string, number>;
  religions: Record<string, number>;
}

export interface HouseholdStats {
  totalHouseholds: number;
  averageHouseholdSize: number;
  householdSizeDistribution: Record<string, number>;
  householdsWithMultipleSupporters: number;
  supporterStatusByHousehold: {
    allSupporters: number;
    mixedSupport: number;
    noSupporters: number;
  };
  topHouseholds: Array<{
    id: string;
    relationshipName: string;
    houseNumber: string;
    memberCount: number;
    supporterCount: number;
    pollingStation: string;
  }>;
}

export interface BeneficiaryStats {
  totalAmount: number;
  totalTransactions: number;
  averageAmount: number;
  topBeneficiaries: Array<{
    voterId: number;
    voterName: string;
    totalAmount: number;
    transactionCount: number;
    supporterStatus?: string;
  }>;
  amountByMonth: Record<string, number>;
  amountBySupporterStatus: Record<string, number>;
  amountByPollingStation: Record<string, number>;
  transactionPurposes: Record<string, number>;
}

export interface ReportFilters extends VoterFilters {
  dateFrom?: string;
  dateTo?: string;
  amountFrom?: number;
  amountTo?: number;
  householdSizeFrom?: number;
  householdSizeTo?: number;
}

export class ReportingService {
  /**
   * Generate polling station report statistics
   */
  static generatePollingStationReport(
    voters: VoterData[],
    filters: ReportFilters = {}
  ): PollingStationStats[] {
    // Filter voters based on criteria
    const filteredVoters = this.filterVoters(voters, filters);
    
    // Group by polling station
    const stationGroups = this.groupByPollingStation(filteredVoters);
    
    return Object.entries(stationGroups).map(([stationName, stationVoters]) => {
      return this.calculatePollingStationStats(stationName, stationVoters);
    });
  }

  /**
   * Generate household report statistics
   */
  static generateHouseholdReport(
    households: Household[],
    filters: ReportFilters = {}
  ): HouseholdStats {
    // Filter households based on criteria
    const filteredHouseholds = this.filterHouseholds(households, filters);
    
    return this.calculateHouseholdStats(filteredHouseholds);
  }

  /**
   * Generate beneficiary report statistics
   */
  static generateBeneficiaryReport(
    transactions: Transaction[],
    voters: VoterData[],
    filters: ReportFilters = {}
  ): BeneficiaryStats {
    // Filter transactions based on criteria
    const filteredTransactions = this.filterTransactions(transactions, filters);
    
    return this.calculateBeneficiaryStats(filteredTransactions, voters);
  }

  /**
   * Filter voters based on report criteria
   */
  private static filterVoters(voters: VoterData[], filters: ReportFilters): VoterData[] {
    return voters.filter(voter => {
      // Apply standard voter filters
      if (filters.gender && voter.gender !== filters.gender) return false;
      if (filters.status && voter.status !== filters.status) return false;
      if (filters.polling_station && voter.polling_station !== filters.polling_station) return false;
      if (filters.section && voter.section !== filters.section) return false;
      if (filters.community && voter.community !== filters.community) return false;
      if (filters.religion && voter.religion !== filters.religion) return false;
      if (filters.supporter_status && voter.supporter_status !== filters.supporter_status) return false;
      
      // Age filters
      if (filters.ageFrom || filters.ageTo) {
        const age = voter.birth_year ? new Date().getFullYear() - voter.birth_year : 0;
        if (filters.ageFrom && age < filters.ageFrom) return false;
        if (filters.ageTo && age > filters.ageTo) return false;
      }
      
      return true;
    });
  }

  /**
   * Filter households based on criteria
   */
  private static filterHouseholds(households: Household[], filters: ReportFilters): Household[] {
    return households.filter(household => {
      if (filters.polling_station && household.polling_station !== filters.polling_station) return false;
      if (filters.section && household.section !== filters.section) return false;
      if (filters.householdSizeFrom && household.memberCount < filters.householdSizeFrom) return false;
      if (filters.householdSizeTo && household.memberCount > filters.householdSizeTo) return false;
      
      return true;
    });
  }

  /**
   * Filter transactions based on criteria
   */
  private static filterTransactions(transactions: Transaction[], filters: ReportFilters): Transaction[] {
    return transactions.filter(transaction => {
      // Date filters
      if (filters.dateFrom || filters.dateTo) {
        const transactionDate = this.parseDate(transaction.date);
        if (filters.dateFrom && transactionDate < new Date(filters.dateFrom)) return false;
        if (filters.dateTo && transactionDate > new Date(filters.dateTo)) return false;
      }
      
      // Amount filters
      if (filters.amountFrom && transaction.amount < filters.amountFrom) return false;
      if (filters.amountTo && transaction.amount > filters.amountTo) return false;
      
      return true;
    });
  }

  /**
   * Group voters by polling station
   */
  private static groupByPollingStation(voters: VoterData[]): Record<string, VoterData[]> {
    return voters.reduce((groups, voter) => {
      const station = voter.polling_station || 'Unknown';
      if (!groups[station]) {
        groups[station] = [];
      }
      groups[station].push(voter);
      return groups;
    }, {} as Record<string, VoterData[]>);
  }

  /**
   * Calculate statistics for a polling station
   */
  private static calculatePollingStationStats(stationName: string, voters: VoterData[]): PollingStationStats {
    const totalVoters = voters.length;
    const activeVoters = voters.filter(v => v.status === 'Active').length;
    const inactiveVoters = totalVoters - activeVoters;
    const maleVoters = voters.filter(v => v.gender === 'Male').length;
    const femaleVoters = voters.filter(v => v.gender === 'Female').length;
    
    // Supporter status counts
    const strongSupporters = voters.filter(v => v.supporter_status === 'Strong Supporter').length;
    const potentialSupporters = voters.filter(v => v.supporter_status === 'Potential Supporter').length;
    const undecided = voters.filter(v => v.supporter_status === 'Undecided').length;
    const opposed = voters.filter(v => v.supporter_status === 'Opposed').length;
    
    // Age group distribution
    const ageGroups = {
      '18-25': 0,
      '26-35': 0,
      '36-45': 0,
      '46-55': 0,
      '56-65': 0,
      '65+': 0,
    };
    
    voters.forEach(voter => {
      if (voter.birth_year) {
        const age = new Date().getFullYear() - voter.birth_year;
        if (age >= 18 && age <= 25) ageGroups['18-25']++;
        else if (age >= 26 && age <= 35) ageGroups['26-35']++;
        else if (age >= 36 && age <= 45) ageGroups['36-45']++;
        else if (age >= 46 && age <= 55) ageGroups['46-55']++;
        else if (age >= 56 && age <= 65) ageGroups['56-65']++;
        else if (age > 65) ageGroups['65+']++;
      }
    });
    
    // Community and religion distribution
    const communities = this.countByField(voters, 'community');
    const religions = this.countByField(voters, 'religion');
    
    return {
      stationName,
      totalVoters,
      activeVoters,
      inactiveVoters,
      maleVoters,
      femaleVoters,
      strongSupporters,
      potentialSupporters,
      undecided,
      opposed,
      ageGroups,
      communities,
      religions,
    };
  }

  /**
   * Calculate household statistics
   */
  private static calculateHouseholdStats(households: Household[]): HouseholdStats {
    const totalHouseholds = households.length;
    const totalMembers = households.reduce((sum, h) => sum + h.memberCount, 0);
    const averageHouseholdSize = totalHouseholds > 0 ? Math.round((totalMembers / totalHouseholds) * 10) / 10 : 0;
    
    // Household size distribution
    const householdSizeDistribution: Record<string, number> = {};
    households.forEach(household => {
      const size = household.memberCount.toString();
      householdSizeDistribution[size] = (householdSizeDistribution[size] || 0) + 1;
    });
    
    // Count households with multiple supporters
    let householdsWithMultipleSupporters = 0;
    let allSupporters = 0;
    let mixedSupport = 0;
    let noSupporters = 0;
    
    households.forEach(household => {
      const supporters = household.members.filter(m => 
        m.supporter_status === 'Strong Supporter' || m.supporter_status === 'Potential Supporter'
      );
      
      if (supporters.length > 1) {
        householdsWithMultipleSupporters++;
      }
      
      if (supporters.length === household.memberCount) {
        allSupporters++;
      } else if (supporters.length > 0) {
        mixedSupport++;
      } else {
        noSupporters++;
      }
    });
    
    // Top households by member count
    const topHouseholds = households
      .sort((a, b) => b.memberCount - a.memberCount)
      .slice(0, 10)
      .map(household => ({
        id: household.id,
        relationshipName: household.relationship_name,
        houseNumber: household.house_number,
        memberCount: household.memberCount,
        supporterCount: household.members.filter(m => 
          m.supporter_status === 'Strong Supporter' || m.supporter_status === 'Potential Supporter'
        ).length,
        pollingStation: household.polling_station || 'Unknown',
      }));
    
    return {
      totalHouseholds,
      averageHouseholdSize,
      householdSizeDistribution,
      householdsWithMultipleSupporters,
      supporterStatusByHousehold: {
        allSupporters,
        mixedSupport,
        noSupporters,
      },
      topHouseholds,
    };
  }

  /**
   * Calculate beneficiary statistics
   */
  private static calculateBeneficiaryStats(transactions: Transaction[], voters: VoterData[]): BeneficiaryStats {
    const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
    const totalTransactions = transactions.length;
    const averageAmount = totalTransactions > 0 ? Math.round(totalAmount / totalTransactions) : 0;
    
    // Create voter lookup map
    const voterMap = new Map(voters.map(v => [v.id!, v]));
    
    // Group transactions by voter
    const voterTransactions = new Map<number, Transaction[]>();
    transactions.forEach(transaction => {
      const voterId = transaction.voter_id;
      if (!voterTransactions.has(voterId)) {
        voterTransactions.set(voterId, []);
      }
      voterTransactions.get(voterId)!.push(transaction);
    });
    
    // Calculate top beneficiaries
    const topBeneficiaries = Array.from(voterTransactions.entries())
      .map(([voterId, voterTxns]) => {
        const voter = voterMap.get(voterId);
        const voterTotal = voterTxns.reduce((sum, t) => sum + t.amount, 0);
        
        return {
          voterId,
          voterName: voter?.name || 'Unknown',
          totalAmount: voterTotal,
          transactionCount: voterTxns.length,
          supporterStatus: voter?.supporter_status,
        };
      })
      .sort((a, b) => b.totalAmount - a.totalAmount)
      .slice(0, 10);
    
    // Amount by month
    const amountByMonth: Record<string, number> = {};
    transactions.forEach(transaction => {
      const date = this.parseDate(transaction.date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      amountByMonth[monthKey] = (amountByMonth[monthKey] || 0) + transaction.amount;
    });
    
    // Amount by supporter status
    const amountBySupporterStatus: Record<string, number> = {};
    transactions.forEach(transaction => {
      const voter = voterMap.get(transaction.voter_id);
      const status = voter?.supporter_status || 'Unknown';
      amountBySupporterStatus[status] = (amountBySupporterStatus[status] || 0) + transaction.amount;
    });
    
    // Amount by polling station
    const amountByPollingStation: Record<string, number> = {};
    transactions.forEach(transaction => {
      const voter = voterMap.get(transaction.voter_id);
      const station = voter?.polling_station || 'Unknown';
      amountByPollingStation[station] = (amountByPollingStation[station] || 0) + transaction.amount;
    });
    
    // Transaction purposes
    const transactionPurposes = this.countByField(transactions, 'purpose');
    
    return {
      totalAmount,
      totalTransactions,
      averageAmount,
      topBeneficiaries,
      amountByMonth,
      amountBySupporterStatus,
      amountByPollingStation,
      transactionPurposes,
    };
  }

  /**
   * Count occurrences of a field value
   */
  private static countByField<T>(items: T[], field: keyof T): Record<string, number> {
    return items.reduce((counts, item) => {
      const value = String(item[field] || 'Unknown');
      counts[value] = (counts[value] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);
  }

  /**
   * Parse date string in DD-MM-YYYY format
   */
  private static parseDate(dateString: string): Date {
    const [day, month, year] = dateString.split('-').map(Number);
    return new Date(year, month - 1, day);
  }
}
