import { save } from '@tauri-apps/plugin-dialog';
import { writeTextFile, writeFile } from '@tauri-apps/plugin-fs';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { ExportOptions } from './ExportService';

export class TauriExportService {
  /**
   * Check if running in Tauri environment
   */
  private static isTauri(): boolean {
    return typeof window !== 'undefined' && window.__TAURI__;
  }

  /**
   * Export data to PDF using Tauri file system
   */
  public static async exportToPDF(options: ExportOptions): Promise<void> {
    const { columns, data, filename, title, filters } = options;

    // Filter only visible columns
    const visibleColumns = columns.filter(col => col.visible);

    if (visibleColumns.length === 0) {
      throw new Error('Please select at least one column to export');
    }

    if (!this.isTauri()) {
      throw new Error('This export method requires Tauri environment');
    }

    try {
      // Create PDF document
      const doc = new jsPDF('l', 'mm', 'a4'); // Landscape for better table fit
      const pageWidth = doc.internal.pageSize.getWidth();
      let yPosition = 20;

      // Add title
      if (title) {
        doc.setFontSize(16);
        doc.setFont('helvetica', 'bold');
        doc.text(title, pageWidth / 2, yPosition, { align: 'center' });
        yPosition += 15;
      }

      // Add export date
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(`Generated on: ${new Date().toLocaleString()}`, 20, yPosition);
      yPosition += 10;

      // Add filters summary if provided
      if (filters) {
        doc.text(`Total Records: ${data.length}`, 20, yPosition);
        yPosition += 15;
      }

      // Prepare table data
      const headers = visibleColumns.map(col => col.label);
      const rows = data.map(item =>
        visibleColumns.map(col => {
          const value = item[col.key];
          return value !== null && value !== undefined ? String(value) : '';
        })
      );

      // Add table with better column sizing
      autoTable(doc, {
        startY: yPosition,
        head: [headers],
        body: rows,
        theme: 'striped',
        headStyles: {
          fillColor: [26, 115, 232],
          fontSize: 8,
          fontStyle: 'bold'
        },
        bodyStyles: {
          fontSize: 7,
          cellPadding: 2
        },
        margin: { left: 10, right: 10 },
        tableWidth: 'auto',
        columnStyles: {
          // Make columns auto-size based on content
        },
        styles: {
          overflow: 'linebreak',
          cellWidth: 'wrap'
        }
      });

      // Generate PDF as Uint8Array
      const pdfData = doc.output('arraybuffer');
      const uint8Array = new Uint8Array(pdfData);

      // Use Tauri save dialog
      const defaultFilename = filename || `voters_export_${new Date().toISOString().split('T')[0]}.pdf`;
      const filePath = await save({
        defaultPath: defaultFilename,
        filters: [{
          name: 'PDF Files',
          extensions: ['pdf']
        }]
      });

      if (filePath) {
        await writeFile(filePath, uint8Array);
        console.log('✅ PDF exported successfully to:', filePath);
      }
    } catch (error) {
      console.error('❌ PDF export failed:', error);
      throw new Error(`PDF export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export data to CSV using Tauri file system
   */
  public static async exportToCSV(options: ExportOptions): Promise<void> {
    const { columns, data, filename } = options;

    // Filter only visible columns
    const visibleColumns = columns.filter(col => col.visible);

    if (visibleColumns.length === 0) {
      throw new Error('Please select at least one column to export');
    }

    if (!this.isTauri()) {
      throw new Error('This export method requires Tauri environment');
    }

    try {
      // Create CSV headers
      const headers = visibleColumns.map(col => col.label);

      // Create CSV rows
      const rows = data.map(item =>
        visibleColumns.map(col => {
          const value = item[col.key];
          let csvValue = value !== null && value !== undefined ? String(value) : '';

          // Escape quotes and wrap in quotes if contains comma, quote, or newline
          if (csvValue.includes(',') || csvValue.includes('"') || csvValue.includes('\n')) {
            csvValue = '"' + csvValue.replace(/"/g, '""') + '"';
          }

          return csvValue;
        })
      );

      // Combine headers and rows
      const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');

      // Use Tauri save dialog
      const defaultFilename = filename || `voters_export_${new Date().toISOString().split('T')[0]}.csv`;
      const filePath = await save({
        defaultPath: defaultFilename,
        filters: [{
          name: 'CSV Files',
          extensions: ['csv']
        }]
      });

      if (filePath) {
        await writeTextFile(filePath, csvContent);
        console.log('✅ CSV exported successfully to:', filePath);
      }
    } catch (error) {
      console.error('❌ CSV export failed:', error);
      throw new Error(`CSV export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export database backup using Tauri file system
   */
  public static async exportDatabaseBackup(data: Uint8Array, filename?: string): Promise<void> {
    if (!this.isTauri()) {
      throw new Error('This export method requires Tauri environment');
    }

    try {
      const defaultFilename = filename || `electixir_backup_${new Date().toISOString().split('T')[0]}.json`;
      const filePath = await save({
        defaultPath: defaultFilename,
        filters: [{
          name: 'JSON Files',
          extensions: ['json']
        }]
      });

      if (filePath) {
        await writeFile(filePath, data);
        console.log('✅ Database backup exported successfully to:', filePath);
      }
    } catch (error) {
      console.error('❌ Database backup export failed:', error);
      throw new Error(`Database backup export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
