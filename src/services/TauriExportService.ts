import { save } from '@tauri-apps/plugin-dialog';
import { writeTextFile, writeFile } from '@tauri-apps/plugin-fs';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { ExportOptions } from './ExportService';

export class TauriExportService {
  /**
   * Check if running in Tauri environment
   */
  private static isTauri(): boolean {
    return typeof window !== 'undefined' && (
      (window as any).__TAURI_INTERNALS__ ||
      (window as any).__TAURI__
    );
  }

  /**
   * Export data to PDF using Tauri file system
   */
  public static async exportToPDF(options: ExportOptions): Promise<void> {
    const { columns, data, filename, title, filters } = options;

    // Filter only visible columns
    const visibleColumns = columns.filter(col => col.visible);

    if (visibleColumns.length === 0) {
      throw new Error('Please select at least one column to export');
    }

    if (!this.isTauri()) {
      throw new Error('This export method requires Tauri environment');
    }

    try {
      // Create PDF document
      const doc = new jsPDF('l', 'mm', 'a4'); // Landscape for better table fit
      const pageWidth = doc.internal.pageSize.getWidth();
      let yPosition = 20;

      // Add title
      if (title) {
        doc.setFontSize(16);
        doc.setFont('helvetica', 'bold');
        doc.text(title, pageWidth / 2, yPosition, { align: 'center' });
        yPosition += 15;
      }

      // Add export date
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(`Generated on: ${new Date().toLocaleString()}`, 20, yPosition);
      yPosition += 10;

      // Add filters summary if provided
      if (filters) {
        doc.text(`Total Records: ${data.length}`, 20, yPosition);
        yPosition += 15;
      }

      // Prepare table data
      const headers = visibleColumns.map(col => col.label);
      const rows = data.map(item =>
        visibleColumns.map(col => {
          const value = item[col.key];
          return value !== null && value !== undefined ? String(value) : '';
        })
      );

      // Add table with better column sizing
      autoTable(doc, {
        startY: yPosition,
        head: [headers],
        body: rows,
        theme: 'striped',
        headStyles: {
          fillColor: [26, 115, 232],
          fontSize: 8,
          fontStyle: 'bold'
        },
        bodyStyles: {
          fontSize: 7,
          cellPadding: 2
        },
        margin: { left: 10, right: 10 },
        tableWidth: 'auto',
        columnStyles: {
          // Make columns auto-size based on content
        },
        styles: {
          overflow: 'linebreak',
          cellWidth: 'wrap'
        }
      });

      // Generate PDF as Uint8Array
      const pdfData = doc.output('arraybuffer');
      const uint8Array = new Uint8Array(pdfData);

      // Use Tauri save dialog
      const defaultFilename = filename || `voters_export_${new Date().toISOString().split('T')[0]}.pdf`;
      const filePath = await save({
        defaultPath: defaultFilename,
        filters: [{
          name: 'PDF Files',
          extensions: ['pdf']
        }]
      });

      if (filePath) {
        await writeFile(filePath, uint8Array);
        console.log('✅ PDF exported successfully to:', filePath);
      }
    } catch (error) {
      console.error('❌ PDF export failed:', error);
      throw new Error(`PDF export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export data to CSV using Tauri file system
   */
  public static async exportToCSV(options: ExportOptions): Promise<void> {
    const { columns, data, filename } = options;

    // Filter only visible columns
    const visibleColumns = columns.filter(col => col.visible);

    if (visibleColumns.length === 0) {
      throw new Error('Please select at least one column to export');
    }

    if (!this.isTauri()) {
      throw new Error('This export method requires Tauri environment');
    }

    try {
      // Create CSV headers
      const headers = visibleColumns.map(col => col.label);

      // Create CSV rows
      const rows = data.map(item =>
        visibleColumns.map(col => {
          const value = item[col.key];
          let csvValue = value !== null && value !== undefined ? String(value) : '';

          // Escape quotes and wrap in quotes if contains comma, quote, or newline
          if (csvValue.includes(',') || csvValue.includes('"') || csvValue.includes('\n')) {
            csvValue = '"' + csvValue.replace(/"/g, '""') + '"';
          }

          return csvValue;
        })
      );

      // Combine headers and rows
      const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');

      // Use Tauri save dialog
      const defaultFilename = filename || `voters_export_${new Date().toISOString().split('T')[0]}.csv`;
      const filePath = await save({
        defaultPath: defaultFilename,
        filters: [{
          name: 'CSV Files',
          extensions: ['csv']
        }]
      });

      if (filePath) {
        await writeTextFile(filePath, csvContent);
        console.log('✅ CSV exported successfully to:', filePath);
      }
    } catch (error) {
      console.error('❌ CSV export failed:', error);
      throw new Error(`CSV export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export database backup as compressed SQLite file using Tauri file system
   */
  public static async exportDatabaseBackup(filename?: string): Promise<void> {
    if (!this.isTauri()) {
      throw new Error('This export method requires Tauri environment');
    }

    try {
      const JSZip = (await import('jszip')).default;
      const { readFile, BaseDirectory, exists } = await import('@tauri-apps/plugin-fs');

      // Check if database file exists in AppData directory
      console.log('🔍 Checking if database file exists in AppData...');
      let dbExists = await exists('electixir.db', {
        baseDir: BaseDirectory.AppData
      });
      let baseDir = BaseDirectory.AppData;

      // If not found in AppData, try AppLocalData (fallback)
      if (!dbExists) {
        console.log('🔍 Database not found in AppData, checking AppLocalData...');
        dbExists = await exists('electixir.db', {
          baseDir: BaseDirectory.AppLocalData
        });
        if (dbExists) {
          baseDir = BaseDirectory.AppLocalData;
        }
      }

      if (!dbExists) {
        throw new Error('Database file not found in AppData or AppLocalData directories. Please ensure the database is initialized.');
      }

      // Read the actual SQLite database file
      console.log(`📖 Reading SQLite database file from ${baseDir === BaseDirectory.AppData ? 'AppData' : 'AppLocalData'}...`);
      const dbData = await readFile('electixir.db', {
        baseDir: baseDir
      });

      if (!dbData || dbData.length === 0) {
        throw new Error('Database file is empty or could not be read.');
      }

      // Create ZIP archive
      console.log('🗜️ Creating ZIP archive...');
      const zip = new JSZip();
      const dateStr = new Date().toISOString().split('T')[0];
      const dbFileName = `electixir_backup_${dateStr}.sqlite`;

      // Add the SQLite file to the ZIP
      zip.file(dbFileName, dbData);

      // Generate ZIP file as Uint8Array
      console.log('📦 Generating ZIP file...');
      const zipData = await zip.generateAsync({
        type: 'uint8array',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      });

      // Use Tauri save dialog
      const defaultFilename = filename || `electixir_backup_${dateStr}.zip`;
      const filePath = await save({
        defaultPath: defaultFilename,
        filters: [{
          name: 'ZIP Files',
          extensions: ['zip']
        }]
      });

      if (filePath) {
        await writeFile(filePath, zipData);
        console.log('✅ Database backup exported successfully to:', filePath);
        console.log(`📊 Original size: ${dbData.length} bytes, Compressed size: ${zipData.length} bytes`);
      } else {
        console.log('ℹ️ Export cancelled by user');
      }
    } catch (error) {
      console.error('❌ Database backup export failed:', error);
      throw new Error(`Database backup export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Import database backup from compressed SQLite file using Tauri file system
   */
  public static async importDatabaseBackup(): Promise<void> {
    if (!this.isTauri()) {
      throw new Error('This import method requires Tauri environment');
    }

    try {
      const JSZip = (await import('jszip')).default;
      const { open } = await import('@tauri-apps/plugin-dialog');
      const { readFile, writeFile, BaseDirectory } = await import('@tauri-apps/plugin-fs');

      // Open file dialog to select backup file
      const filePath = await open({
        filters: [{
          name: 'Backup Files',
          extensions: ['zip', 'sqlite', 'db']
        }]
      });

      if (!filePath) {
        return; // User cancelled
      }

      console.log('📖 Reading backup file:', filePath);
      const fileData = await readFile(filePath);

      if (!fileData || fileData.length === 0) {
        throw new Error('Backup file is empty or could not be read.');
      }

      let sqliteData: Uint8Array;

      if (filePath.toLowerCase().endsWith('.zip')) {
        // Handle ZIP file
        console.log('🗜️ Extracting ZIP file...');
        const zip = new JSZip();
        const zipContent = await zip.loadAsync(fileData);

        // Find the SQLite file in the ZIP
        const sqliteFiles = Object.keys(zipContent.files).filter(
          name => name.toLowerCase().endsWith('.sqlite') ||
                  name.toLowerCase().endsWith('.db') ||
                  name.includes('electixir')
        );

        if (sqliteFiles.length === 0) {
          throw new Error('No SQLite database file found in ZIP archive. Expected files with .sqlite or .db extension.');
        }

        if (sqliteFiles.length > 1) {
          throw new Error(`Multiple database files found in ZIP: ${sqliteFiles.join(', ')}. Please use a backup with only one database file.`);
        }

        // Extract the SQLite file
        console.log(`📂 Extracting database file: ${sqliteFiles[0]}`);
        const sqliteFile = zipContent.files[sqliteFiles[0]];
        const arrayBuffer = await sqliteFile.async('arraybuffer');
        sqliteData = new Uint8Array(arrayBuffer);
      } else {
        // Handle direct SQLite file
        console.log('📂 Using direct SQLite file');
        sqliteData = fileData;
      }

      if (!sqliteData || sqliteData.length === 0) {
        throw new Error('Extracted database file is empty.');
      }

      // Determine where to write the database file (check where it currently exists)
      const { exists } = await import('@tauri-apps/plugin-fs');
      let targetBaseDir = BaseDirectory.AppData;

      // Check if database already exists in AppLocalData
      const existsInAppLocalData = await exists('electixir.db', {
        baseDir: BaseDirectory.AppLocalData
      });

      if (existsInAppLocalData) {
        targetBaseDir = BaseDirectory.AppLocalData;
      }

      // Write the SQLite file to replace the current database
      console.log(`💾 Restoring database file to ${targetBaseDir === BaseDirectory.AppData ? 'AppData' : 'AppLocalData'}...`);
      await writeFile('electixir.db', sqliteData, {
        baseDir: targetBaseDir
      });

      console.log('✅ Database restored successfully');
      console.log(`📊 Restored database size: ${sqliteData.length} bytes`);
    } catch (error) {
      console.error('❌ Database import failed:', error);
      throw new Error(`Database import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
