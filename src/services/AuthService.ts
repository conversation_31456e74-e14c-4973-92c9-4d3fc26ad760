import bcrypt from 'bcryptjs';
import { DatabaseService } from '../database/DatabaseService';

export interface User {
  id: number;
  username: string;
  role: 'admin' | 'user';
}

/**
 * Authentication service for user management and session handling
 * Uses bcrypt for password hashing and localStorage for session persistence
 */
export class AuthService {
  private dbService: DatabaseService;

  constructor() {
    this.dbService = DatabaseService.getInstance();
  }

  /**
   * Validate user credentials
   * @param username - Username to validate
   * @param password - Plain text password
   * @returns User object if valid, null if invalid
   */
  async validateUser(username: string, password: string): Promise<User | null> {
    const db = this.dbService.getDatabase();
    if (!db) {
      console.error('Database not available for user validation');
      return null;
    }

    try {
      console.log('🔍 Attempting to validate user:', username);
      const rows = await db.select('SELECT * FROM users WHERE username = ?', [username.trim()]) as any[];
      console.log('🔍 Found users:', rows.length);

      if (rows.length > 0) {
        const row = rows[0];
        console.log('🔍 User found:', { id: row.id, username: row.username, role: row.role });

        // Verify password with bcrypt
        const isValid = await bcrypt.compare(password, row.password_hash as string);
        console.log('🔍 Password valid:', isValid);

        if (isValid) {
          return {
            id: row.id as number,
            username: row.username as string,
            role: row.role as 'admin' | 'user'
          };
        }
      } else {
        console.log('🔍 No user found with username:', username);
      }
    } catch (error) {
      console.error('Error validating user:', error);
    }

    return null;
  }

  /**
   * Create a new user with hashed password
   * @param username - Username for new user
   * @param password - Plain text password
   * @param role - User role (admin or user)
   * @returns True if user created successfully
   */
  async createUser(username: string, password: string, role: 'admin' | 'user' = 'user'): Promise<boolean> {
    const db = this.dbService.getDatabase();
    if (!db) {
      console.error('Database not available for user creation');
      return false;
    }

    // Validate input
    if (!username.trim() || password.length < 6) {
      console.error('Invalid username or password (minimum 6 characters)');
      return false;
    }

    try {
      // Check if user already exists
      const existingRows = await db.select('SELECT COUNT(*) as count FROM users WHERE username = ?', [username.trim()]) as any[];
      const count = existingRows.length > 0 ? existingRows[0].count : 0;

      if (count > 0) {
        console.error('User already exists:', username);
        return false;
      }

      // Hash password with bcrypt
      const saltRounds = 10;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // Insert new user
      await db.execute('INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)', [username.trim(), passwordHash, role]);

      console.log(`✅ User created successfully: ${username} (${role})`);
      return true;
    } catch (error) {
      console.error('Failed to create user:', error);
      return false;
    }
  }

  /**
   * Get all users (admin only)
   * @returns Array of users without password hashes
   */
  async getAllUsers(): Promise<User[]> {
    const db = this.dbService.getDatabase();
    if (!db) return [];

    try {
      const rows = await db.select('SELECT id, username, role FROM users WHERE username != ? ORDER BY username', ['admin']) as any[];
      return rows.map(row => ({
        id: row.id as number,
        username: row.username as string,
        role: row.role as 'admin' | 'user'
      }));
    } catch (error) {
      console.error('Failed to get users:', error);
      return [];
    }
  }

  /**
   * Delete a user (cannot delete admin)
   * @param userId - ID of user to delete
   * @returns True if deleted successfully
   */
  async deleteUser(userId: number): Promise<boolean> {
    const db = this.dbService.getDatabase();
    if (!db) return false;

    try {
      // Check if user is admin
      const rows = await db.select('SELECT username, role FROM users WHERE id = ?', [userId]) as any[];

      if (rows.length === 0) {
        return false;
      }

      if (rows[0].username === 'admin') {
        console.error('Cannot delete admin user');
        return false;
      }

      // Delete user
      await db.execute('DELETE FROM users WHERE id = ?', [userId]);

      console.log(`✅ User deleted successfully: ID ${userId}`);
      return true;
    } catch (error) {
      console.error('Failed to delete user:', error);
      return false;
    }
  }

  /**
   * Promote a user to admin
   * @param userId - ID of user to promote
   * @returns True if promoted successfully
   */
  async promoteUser(userId: number): Promise<boolean> {
    const db = this.dbService.getDatabase();
    if (!db) return false;

    try {
      await db.execute('UPDATE users SET role = ? WHERE id = ?', [
        'admin',
        userId,
      ]);
      console.log(`✅ User promoted to admin: ID ${userId}`);
      return true;
    } catch (error) {
      console.error('Failed to promote user:', error);
      return false;
    }
  }

  /**
   * Ensure default admin user exists
   * Called during database initialization
   */
  async ensureDefaultAdmin(): Promise<void> {
    const db = this.dbService.getDatabase();
    if (!db) {
      console.error('❌ Database not available for ensureDefaultAdmin');
      return;
    }

    try {
      console.log('🔍 Checking if admin user exists...');
      // Check if admin exists
      const rows = await db.select('SELECT COUNT(*) as count FROM users WHERE username = ?', ['admin']) as any[];
      const count = rows.length > 0 ? rows[0].count : 0;
      console.log('🔍 Admin user count:', count);

      if (count === 0) {
        console.log('🔄 Creating default admin user...');
        // Create default admin with bcrypt hash
        const success = await this.createUser('admin', 'mawhati8123', 'admin');
        if (success) {
          console.log('✅ Default admin user created with username: admin, password: mawhati8123');
        } else {
          console.error('❌ Failed to create default admin user');
        }
      } else {
        console.log('✅ Admin user already exists');
        // Let's also check what users exist
        const allUsers = await db.select('SELECT username, role FROM users') as any[];
        console.log('🔍 All users in database:', allUsers);
      }
    } catch (error) {
      console.error('Error ensuring default admin:', error);
    }
  }

  /**
   * Save user session to localStorage
   * @param user - User object to save
   */
  saveSession(user: User): void {
    try {
      localStorage.setItem('auth_user', JSON.stringify(user));
      console.log(`✅ Session saved for user: ${user.username}`);
    } catch (error) {
      console.error('Failed to save session:', error);
    }
  }

  /**
   * Get current user session from localStorage
   * @returns User object if session exists, null otherwise
   */
  getSession(): User | null {
    try {
      const stored = localStorage.getItem('auth_user');
      if (stored) {
        const user = JSON.parse(stored);
        return user;
      }
    } catch (error) {
      console.error('Failed to get session:', error);
    }
    return null;
  }

  /**
   * Clear current user session
   */
  clearSession(): void {
    try {
      localStorage.removeItem('auth_user');
      console.log('✅ Session cleared');
    } catch (error) {
      console.error('Failed to clear session:', error);
    }
  }
}
