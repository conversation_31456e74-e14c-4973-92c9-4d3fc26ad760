import { createContext, useContext, useEffect, ReactNode } from 'react';
import { useLocalStorageString } from '../hooks/useLocalStorage';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  isDarkTheme: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

/**
 * Theme provider component that manages theme state and persistence
 */
export function ThemeProvider({ children }: ThemeProviderProps) {
  // Get initial theme from localStorage or system preference
  const getInitialTheme = (): Theme => {
    // Check if we have a saved theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'light' || savedTheme === 'dark') {
      return savedTheme;
    }

    // Fallback to system preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  };

  const [theme, setThemeValue] = useLocalStorageString('theme', getInitialTheme());
  const typedTheme = theme as Theme;

  // Apply theme to document
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', typedTheme);
  }, [typedTheme]);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e: MediaQueryListEvent) => {
      // Only auto-switch if user hasn't manually set a preference
      const savedTheme = localStorage.getItem('theme');
      if (!savedTheme) {
        const systemTheme = e.matches ? 'dark' : 'light';
        setThemeValue(systemTheme);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [setThemeValue]);

  const toggleTheme = () => {
    const newTheme = typedTheme === 'dark' ? 'light' : 'dark';
    setThemeValue(newTheme);
  };

  const setTheme = (newTheme: Theme) => {
    setThemeValue(newTheme);
  };

  const isDarkTheme = typedTheme === 'dark';

  const value: ThemeContextType = {
    theme: typedTheme,
    toggleTheme,
    setTheme,
    isDarkTheme,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
}

/**
 * Hook to use theme context
 */
export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
