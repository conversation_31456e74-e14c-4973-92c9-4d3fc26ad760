import { createContext, useContext, useReducer, useEffect, ReactNode, useCallback } from 'react';
import { AuthService, User } from '../services/AuthService';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthContextType {
  state: AuthState;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isAdmin: () => boolean;
  canAccessTransactions: () => boolean;
  canAccessReports: () => boolean;
  canAccessSettings: () => boolean;
  canEditVoters: () => boolean;
}

type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOGOUT' };

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: action.payload !== null,
        isLoading: false,
        error: null,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    default:
      return state;
  }
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const authService = new AuthService();

  // Check for existing session on mount
  useEffect(() => {
    const checkSession = () => {
      try {
        const user = authService.getSession();
        if (user) {
          dispatch({ type: 'SET_USER', payload: user });
        } else {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      } catch (error) {
        console.error('Failed to check session:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Failed to restore session' });
      }
    };

    checkSession();
  }, []);

  const login = useCallback(async (username: string, password: string): Promise<boolean> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const user = await authService.validateUser(username, password);
      if (user) {
        authService.saveSession(user);
        dispatch({ type: 'SET_USER', payload: user });
        return true;
      } else {
        dispatch({ type: 'SET_ERROR', payload: 'Invalid username or password' });
        return false;
      }
    } catch (error) {
      console.error('Login failed:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Login failed. Please try again.' });
      return false;
    }
  }, [authService]);

  const logout = useCallback(() => {
    authService.clearSession();
    dispatch({ type: 'LOGOUT' });
  }, [authService]);

  const isAdmin = useCallback((): boolean => {
    return state.user?.role === 'admin';
  }, [state.user]);

  const canAccessTransactions = useCallback((): boolean => {
    return state.user?.role === 'admin';
  }, [state.user]);

  const canAccessReports = useCallback((): boolean => {
    return state.user?.role === 'admin';
  }, [state.user]);

  const canAccessSettings = useCallback((): boolean => {
    return state.user?.role === 'admin';
  }, [state.user]);

  const canEditVoters = useCallback((): boolean => {
    return state.user?.role === 'admin';
  }, [state.user]);

  const contextValue: AuthContextType = {
    state,
    login,
    logout,
    isAdmin,
    canAccessTransactions,
    canAccessReports,
    canAccessSettings,
    canEditVoters,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}
