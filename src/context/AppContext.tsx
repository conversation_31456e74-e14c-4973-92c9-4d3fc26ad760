import React, { createContext, useContext, useReducer, ReactNode, useEffect, useMemo } from 'react';
import { useDatabase } from '../hooks/useDatabase';
import SettingsService from '../database/SettingsService';
import { ColumnVisibility } from '../hooks/useColumnVisibility';

// Types
export interface Voter {
  id: number;
  name: string;
  relationship_type?: 'Father' | 'Mother' | 'Husband' | 'Others';
  relationship_name?: string;
  gender: 'Male' | 'Female' | 'Other';
  birth_year?: number;
  epic_number: string;
  house_number?: string;
  polling_station?: string;
  section?: string;

  // Extended contact information
  phone?: string;
  email?: string;
  facebook?: string;
  instagram?: string;
  twitter?: string;

  // Voter status
  status?: 'Active' | 'Expired' | 'Shifted' | 'Duplicate' | 'Missing' | 'Disqualified';

  // Political information
  supporter_status?: 'Strong Supporter' | 'Potential Supporter' | 'Undecided' | 'Opposed';

  // Demographics
  education?: string;
  occupation?: string;
  community?: string;
  religion?: string;
  economic_status?: string;
  custom_notes?: string;

  // Computed fields for backward compatibility
  age?: number; // Computed from birth_year
  epic?: string; // Alias for epic_number
  pollingStation?: string; // Alias for polling_station
  economicStatus?: string; // Alias for economic_status
  fatherName?: string; // Alias for relationship_name when relationship_type is 'Father'
  qualification?: string; // Alias for education
}

export interface Filters {
  gender: string;
  ageFrom: number;
  ageTo: number;
  community: string;
  religion: string;
  economicStatus: string; // Keep for backward compatibility
  economic_status: string; // New field name
  searchTerm: string;
  status: string;
  polling_station: string;
  section: string;
  supporter_status: string;
  // Selected polling stations and sections for filtering
  selectedPollingStations: string[];
  selectedSections: Record<string, string[]>;
}

export interface CategoryData {
  community: string[];
  religion: string[];
  economic_status: string[];
}

export interface AppState {
  // UI State
  isFilterPanelOpen: boolean;
  isSettingsDashboardOpen: boolean;
  isVoterPanelOpen: boolean;
  activeDropdown: string | null;
  currentView: 'main' | 'reports';
  selectedVoter: Voter | null;

  // Data State
  voters: Voter[];
  filters: Filters;
  categoryData: CategoryData;

  // Column Visibility State
  columnVisibility: ColumnVisibility;

  // Database State
  isDatabaseInitialized: boolean;

  // Loading State
  isLoading: boolean;
  error: string | null;
  importProgress: string | null;
}

// Action Types
type AppAction =
  | { type: 'TOGGLE_FILTER_PANEL' }
  | { type: 'TOGGLE_SETTINGS_DASHBOARD' }
  | { type: 'TOGGLE_VOTER_PANEL'; payload?: Voter }
  | { type: 'SET_ACTIVE_DROPDOWN'; payload: string | null }
  | { type: 'SET_CURRENT_VIEW'; payload: 'main' | 'reports' }
  | { type: 'UPDATE_FILTER'; payload: { key: keyof Filters; value: any } }
  | { type: 'CLEAR_FILTERS' }
  | { type: 'ADD_CATEGORY_ITEM'; payload: { category: keyof CategoryData; item: string } }
  | { type: 'REMOVE_CATEGORY_ITEM'; payload: { category: keyof CategoryData; item: string } }
  | { type: 'SET_CATEGORY_DATA'; payload: CategoryData }
  | { type: 'TOGGLE_COLUMN'; payload: keyof ColumnVisibility }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_VOTERS'; payload: Voter[] }
  | { type: 'UPDATE_VOTER'; payload: Voter }
  | { type: 'SET_SELECTED_VOTER'; payload: Voter }
  | { type: 'SET_DATABASE_INITIALIZED'; payload: boolean }
  | { type: 'UPDATE_SELECTED_POLLING_STATIONS'; payload: string[] }
  | { type: 'UPDATE_SELECTED_SECTIONS'; payload: Record<string, string[]> }
  | { type: 'SET_IMPORT_PROGRESS'; payload: string | null };

// Initial State
const initialFilters: Filters = {
  gender: 'All',
  ageFrom: 18,
  ageTo: 120,
  community: 'All',
  religion: 'All',
  economicStatus: 'All', // Backward compatibility
  economic_status: 'All',
  searchTerm: '',
  status: 'All',
  polling_station: 'All',
  section: 'All',
  supporter_status: 'All',
  selectedPollingStations: [],
  selectedSections: {},
};

const initialCategoryData: CategoryData = {
  community: [],
  religion: [],
  economic_status: [],
};

const initialColumnVisibility: ColumnVisibility = {
  name: true,
  age: true,
  gender: true,
  epic: true,
  pollingStation: true,
  affiliation: false,
  payee: false,
  status: true,
};

const initialVoters: Voter[] = [];

const initialState: AppState = {
  isFilterPanelOpen: false,
  isSettingsDashboardOpen: false,
  isVoterPanelOpen: false,
  activeDropdown: null,
  currentView: 'main',
  selectedVoter: null,
  voters: initialVoters,
  filters: initialFilters,
  categoryData: initialCategoryData,
  columnVisibility: initialColumnVisibility,
  isDatabaseInitialized: false,
  isLoading: false,
  error: null,
  importProgress: null,
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'TOGGLE_FILTER_PANEL':
      return {
        ...state,
        isFilterPanelOpen: !state.isFilterPanelOpen,
        activeDropdown: null, // Close any open dropdowns
      };

    case 'TOGGLE_SETTINGS_DASHBOARD':
      return {
        ...state,
        isSettingsDashboardOpen: !state.isSettingsDashboardOpen,
        activeDropdown: null, // Close any open dropdowns
      };

    case 'TOGGLE_VOTER_PANEL':
      return {
        ...state,
        isVoterPanelOpen: !state.isVoterPanelOpen,
        selectedVoter: action.payload || state.selectedVoter,
      };

    case 'SET_ACTIVE_DROPDOWN':
      return {
        ...state,
        activeDropdown: action.payload,
      };

    case 'SET_CURRENT_VIEW':
      return {
        ...state,
        currentView: action.payload,
      };

    case 'UPDATE_FILTER':
      return {
        ...state,
        filters: {
          ...state.filters,
          [action.payload.key]: action.payload.value,
        },
      };

    case 'CLEAR_FILTERS':
      return {
        ...state,
        filters: {
          ...initialFilters,
          // Preserve current polling station and section selections from sidebar
          selectedPollingStations: state.filters.selectedPollingStations,
          selectedSections: state.filters.selectedSections,
        },
      };

    case 'ADD_CATEGORY_ITEM':
      const { category, item } = action.payload;
      if (state.categoryData[category].includes(item)) {
        return state; // Item already exists
      }
      return {
        ...state,
        categoryData: {
          ...state.categoryData,
          [category]: [...state.categoryData[category], item],
        },
      };

    case 'REMOVE_CATEGORY_ITEM':
      return {
        ...state,
        categoryData: {
          ...state.categoryData,
          [action.payload.category]: state.categoryData[action.payload.category].filter(
            item => item !== action.payload.item
          ),
        },
      };

    case 'SET_CATEGORY_DATA':
      return {
        ...state,
        categoryData: action.payload,
      };

    case 'TOGGLE_COLUMN':
      return {
        ...state,
        columnVisibility: {
          ...state.columnVisibility,
          [action.payload]: !state.columnVisibility[action.payload],
        },
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
      };

    case 'SET_VOTERS':
      return {
        ...state,
        voters: action.payload,
      };

    case 'UPDATE_VOTER':
      return {
        ...state,
        voters: state.voters.map(voter =>
          voter.id === action.payload.id ? action.payload : voter
        ),
      };

    case 'SET_SELECTED_VOTER':
      return {
        ...state,
        selectedVoter: action.payload,
      };

    case 'SET_DATABASE_INITIALIZED':
      return {
        ...state,
        isDatabaseInitialized: action.payload,
      };

    case 'UPDATE_SELECTED_POLLING_STATIONS':
      return {
        ...state,
        filters: {
          ...state.filters,
          selectedPollingStations: action.payload,
        },
      };

    case 'UPDATE_SELECTED_SECTIONS':
      return {
        ...state,
        filters: {
          ...state.filters,
          selectedSections: action.payload,
        },
      };

    case 'SET_IMPORT_PROGRESS':
      return {
        ...state,
        importProgress: action.payload,
      };

    default:
      return state;
  }
}

// Context
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  // Database operations
  database: {
    loadVoters: (filters?: any) => Promise<void>;
    getHouseholds: (filters?: any) => Promise<any[]>;
    addVoter: (voter: any) => Promise<number>;
    updateVoter: (id: number, voter: any) => Promise<void>;
    deleteVoter: (id: number) => Promise<void>;
    importCSV: (file: File) => Promise<any>;
    exportCSV: (filters?: any) => Promise<string>;
    validateCSV: (file: File) => Promise<{ valid: boolean; errors: string[] }>;
    exportDatabase: () => Promise<Uint8Array>;
    importDatabase: (data: Uint8Array) => Promise<void>;
    clearDatabase: () => Promise<void>;
  };
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider
interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState);
  const database = useDatabase();

  // Sync database state with app state
  useEffect(() => {
    dispatch({ type: 'SET_DATABASE_INITIALIZED', payload: database.isInitialized });
  }, [database.isInitialized]);

  useEffect(() => {
    dispatch({ type: 'SET_LOADING', payload: database.isLoading });
  }, [database.isLoading]);

  useEffect(() => {
    dispatch({ type: 'SET_ERROR', payload: database.error });
  }, [database.error]);

  // Force reload voters when database becomes initialized
  useEffect(() => {
    if (database.isInitialized) {
      console.log('🔄 Database initialized, forcing voter reload...');

      // Initialize default settings and load category data
      const initializeSettings = async () => {
        try {
          const settingsService = new SettingsService();
          await settingsService.initializeDefaultSettings();
          console.log('✅ Settings initialized');

          // Load category data from database
          const [community, religion, economic_status] = await Promise.all([
            settingsService.getCategorySettings('community'),
            settingsService.getCategorySettings('religion'),
            settingsService.getCategorySettings('economic_status'),
          ]);

          dispatch({
            type: 'SET_CATEGORY_DATA',
            payload: {
              community,
              religion,
              economic_status,
            },
          });

          console.log('✅ Category data loaded from database');

          // Run diagnostics in development
          if (process.env.NODE_ENV === 'development') {
            const { DiagnosticService } = await import('../utils/diagnostics');
            DiagnosticService.logDiagnostics();
          }
        } catch (error) {
          console.error('Failed to initialize settings:', error);
        }
      };
      initializeSettings();
    }
  }, [database.isInitialized]);

  // Load voters when data filters change (excluding UI selection filters)
  useEffect(() => {
    if (database.isInitialized) {
      database.loadVoters(state.filters);
    }
  }, [
    database.isInitialized,
    state.filters.gender,
    state.filters.ageFrom,
    state.filters.ageTo,
    state.filters.community,
    state.filters.religion,
    state.filters.economicStatus,
    state.filters.economic_status,
    state.filters.searchTerm,
    state.filters.status,
    state.filters.polling_station,
    state.filters.section,
    state.filters.supporter_status
    // Note: selectedPollingStations and selectedSections are excluded
    // as they are UI state, not data filters
  ]);

  // Sync voters from database
  useEffect(() => {
    console.log(`🔄 Database voters changed: ${database.voters.length} voters`);

    // Always sync, even if empty (to clear UI when database is cleared)
    const appVoters: Voter[] = database.voters
      .filter(voter => voter.id !== undefined) // Filter out voters without IDs
      .map(voter => ({
        ...voter,
        id: voter.id!, // We know it's defined due to filter
        // Add backward compatibility fields
        age: voter.birth_year ? new Date().getFullYear() - voter.birth_year : undefined,
        epic: voter.epic_number,
        pollingStation: voter.polling_station,
        economicStatus: voter.economic_status,
        fatherName: voter.relationship_type === 'Father' ? voter.relationship_name : undefined,
        qualification: voter.education,
      }));

    dispatch({ type: 'SET_VOTERS', payload: appVoters });
    console.log(`✅ Updated app state with ${appVoters.length} voters`);
  }, [database.voters]);

  const contextValue: AppContextType = useMemo(
    () => ({
      state,
      dispatch,
      database,
    }),
    [state, database]
  );

  return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
}

// Hook
export function useApp(): AppContextType {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
