import { BaseService } from './BaseService';

export interface FamilyMember {
  id: number;
  name: string;
  relationship_type?: 'Father' | 'Mother' | 'Husband' | 'Others';
  relationship_name?: string;
  gender: 'Male' | 'Female' | 'Other';
  birth_year?: number;
  age?: number;
  house_number?: string;
  polling_station?: string;
  section?: string;
  relationshipToCurrentVoter: string; // Computed relationship
}

export class FamilyService extends BaseService {
  constructor() {
    super();
  }

  /**
   * Smart family detection - finds family members using multiple strategies
   */
  public async getFamilyMembers(currentVoter: any): Promise<FamilyMember[]> {
    const familyMembers: FamilyMember[] = [];
    const addedIds = new Set<number>(); // Prevent duplicates

    try {
      // Strategy 1: Same house number and address (household members)
      const householdMembers = await this.getHouseholdMembers(currentVoter);
      householdMembers.forEach(member => {
        if (!addedIds.has(member.id)) {
          familyMembers.push(member);
          addedIds.add(member.id);
        }
      });

      // Strategy 2: Same father's name (siblings)
      if (currentVoter.relationship_type === 'Father' && currentVoter.relationship_name) {
        const siblings = await this.getSiblings(currentVoter);
        siblings.forEach(member => {
          if (!addedIds.has(member.id)) {
            familyMembers.push(member);
            addedIds.add(member.id);
          }
        });
      }

      // Strategy 3: Current voter is someone's father/mother (children)
      const children = await this.getChildren(currentVoter);
      children.forEach(member => {
        if (!addedIds.has(member.id)) {
          familyMembers.push(member);
          addedIds.add(member.id);
        }
      });

      // Strategy 4: Spouse relationships
      const spouse = await this.getSpouse(currentVoter);
      if (spouse && !addedIds.has(spouse.id)) {
        familyMembers.push(spouse);
        addedIds.add(spouse.id);
      }

      return familyMembers.sort((a, b) => {
        // Sort by age (older first), then by name
        const ageA = a.age || 0;
        const ageB = b.age || 0;
        if (ageA !== ageB) return ageB - ageA;
        return a.name.localeCompare(b.name);
      });
    } catch (error) {
      console.error('Failed to get family members:', error);
      return [];
    }
  }

  /**
   * Get household members (same house number, polling station, section)
   */
  private async getHouseholdMembers(currentVoter: any): Promise<FamilyMember[]> {
    if (!currentVoter.house_number || !currentVoter.polling_station) {
      return [];
    }

    try {
      const sql = `
        SELECT v.*, ps.name as polling_station_name, s.name as section_name
        FROM voters v
        LEFT JOIN polling_stations ps ON v.polling_station_id = ps.id
        LEFT JOIN sections s ON v.section_id = s.id
        WHERE v.id != ?
        AND v.house_number = ?
        AND ps.name = ?
        ${currentVoter.section ? 'AND s.name = ?' : ''}
      `;

      const params = [currentVoter.id, currentVoter.house_number, currentVoter.polling_station];
      if (currentVoter.section) {
        params.push(currentVoter.section);
      }

      const rows = await this.executeQuery(sql, params);
      return rows.map(row => this.mapToFamilyMember(row, 'Household Member'));
    } catch (error) {
      console.error('Failed to get household members:', error);
      return [];
    }
  }

  /**
   * Get siblings (same father's name)
   */
  private async getSiblings(currentVoter: any): Promise<FamilyMember[]> {
    try {
      const sql = `
        SELECT v.*, ps.name as polling_station_name, s.name as section_name
        FROM voters v
        LEFT JOIN polling_stations ps ON v.polling_station_id = ps.id
        LEFT JOIN sections s ON v.section_id = s.id
        WHERE v.id != ?
        AND v.relationship_name = ?
        AND v.relationship_type = 'Father'
      `;

      const rows = await this.executeQuery(sql, [currentVoter.id, currentVoter.relationship_name]);
      return rows.map(row => this.mapToFamilyMember(row, 'Sibling'));
    } catch (error) {
      console.error('Failed to get siblings:', error);
      return [];
    }
  }

  /**
   * Get children (current voter is their father/mother)
   */
  private async getChildren(currentVoter: any): Promise<FamilyMember[]> {
    try {
      const sql = `
        SELECT v.*, ps.name as polling_station_name, s.name as section_name
        FROM voters v
        LEFT JOIN polling_stations ps ON v.polling_station_id = ps.id
        LEFT JOIN sections s ON v.section_id = s.id
        WHERE v.id != ?
        AND v.relationship_name = ?
        AND (v.relationship_type = 'Father' OR v.relationship_type = 'Mother')
      `;

      const rows = await this.executeQuery(sql, [currentVoter.id, currentVoter.name]);
      return rows.map(row => {
        const relationshipType = 'Child (Son/Daughter)';
        return this.mapToFamilyMember(row, relationshipType);
      });
    } catch (error) {
      console.error('Failed to get children:', error);
      return [];
    }
  }

  /**
   * Get spouse (husband/wife relationship)
   */
  private async getSpouse(currentVoter: any): Promise<FamilyMember | null> {
    try {
      // Look for someone who has current voter as husband/wife
      const sql = `
        SELECT v.*, ps.name as polling_station_name, s.name as section_name
        FROM voters v
        LEFT JOIN polling_stations ps ON v.polling_station_id = ps.id
        LEFT JOIN sections s ON v.section_id = s.id
        WHERE v.id != ?
        AND v.relationship_name = ?
        AND v.relationship_type = 'Husband'
        AND v.house_number = ?
      `;

      const row = await this.executeQuerySingle(sql, [currentVoter.id, currentVoter.name, currentVoter.house_number]);
      return row ? this.mapToFamilyMember(row, 'Spouse') : null;
    } catch (error) {
      console.error('Failed to get spouse:', error);
      return null;
    }
  }

  /**
   * Map database row to FamilyMember
   */
  private mapToFamilyMember(row: any, relationshipToCurrentVoter: string): FamilyMember {
    const age = row.birth_year ? new Date().getFullYear() - row.birth_year : undefined;

    return {
      id: row.id as number,
      name: row.name as string,
      relationship_type: row.relationship_type as 'Father' | 'Mother' | 'Husband' | 'Others',
      relationship_name: row.relationship_name as string,
      gender: row.gender as 'Male' | 'Female' | 'Other',
      birth_year: row.birth_year as number,
      age,
      house_number: row.house_number as string,
      polling_station: row.polling_station_name as string,
      section: row.section_name as string,
      relationshipToCurrentVoter,
    };
  }

  /**
   * Get family statistics
   */
  public async getFamilyStats(currentVoter: any): Promise<{
    totalMembers: number;
    maleMembers: number;
    femaleMembers: number;
    averageAge: number;
  }> {
    const familyMembers = await this.getFamilyMembers(currentVoter);

    const totalMembers = familyMembers.length;
    const maleMembers = familyMembers.filter(m => m.gender === 'Male').length;
    const femaleMembers = familyMembers.filter(m => m.gender === 'Female').length;

    const ages = familyMembers.filter(m => m.age).map(m => m.age!);
    const averageAge =
      ages.length > 0 ? Math.round(ages.reduce((sum, age) => sum + age, 0) / ages.length) : 0;

    return {
      totalMembers,
      maleMembers,
      femaleMembers,
      averageAge,
    };
  }
}

export default FamilyService;
