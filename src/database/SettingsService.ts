import { BaseService } from './BaseService';

export interface SettingData {
  id?: number;
  category: string;
  value: string;
  created_at?: string;
}

export class SettingsService extends BaseService {
  constructor() {
    super();
  }

  /**
   * Get all settings for a category
   */
  public async getCategorySettings(category: string): Promise<string[]> {
    try {
      const rows = await this.executeQuery('SELECT value FROM settings WHERE category = ? ORDER BY value', [category]);
      return rows.map(row => row.value as string);
    } catch (error) {
      console.error('Failed to get category settings:', error);
      throw error;
    }
  }

  /**
   * Add a new setting to a category
   */
  public async addCategorySetting(category: string, value: string): Promise<boolean> {
    try {
      const trimmedValue = value.trim();
      if (!trimmedValue) {
        console.warn('Setting value cannot be empty');
        return false;
      }

      // Check if the setting already exists
      const existing = await this.recordExists('settings', 'category = ? AND value = ?', [category, trimmedValue]);
      if (existing) {
        console.warn(`Setting ${trimmedValue} already exists in ${category}`);
        return false;
      }

      // Insert new setting
      await this.executeInsert('INSERT INTO settings (category, value) VALUES (?, ?)', [category, trimmedValue]);

      return true;
    } catch (error) {
      console.error('Failed to add category setting:', error);
      return false;
    }
  }

  /**
   * Update a setting in a category
   */
  public async updateCategorySetting(
    category: string,
    oldValue: string,
    newValue: string
  ): Promise<boolean> {
    try {
      const trimmedNewValue = newValue.trim();
      if (!trimmedNewValue) {
        console.warn('New setting value cannot be empty');
        return false;
      }

      if (trimmedNewValue === oldValue) {
        return true; // No change needed
      }

      // Check if the new value already exists
      const existing = await this.recordExists('settings', 'category = ? AND value = ?', [category, trimmedNewValue]);
      if (existing) {
        console.warn(`Setting ${trimmedNewValue} already exists in ${category}`);
        return false;
      }

      // Update the setting
      await this.executeUpdate('UPDATE settings SET value = ? WHERE category = ? AND value = ?', [trimmedNewValue, category, oldValue]);

      return true;
    } catch (error) {
      console.error('Failed to update category setting:', error);
      return false;
    }
  }

  /**
   * Remove a setting from a category
   */
  public async removeCategorySetting(category: string, value: string): Promise<boolean> {
    try {
      await this.executeUpdate('DELETE FROM settings WHERE category = ? AND value = ?', [category, value]);
      return true;
    } catch (error) {
      console.error('Failed to remove category setting:', error);
      return false;
    }
  }

  /**
   * Initialize default settings if they don't exist
   */
  public async initializeDefaultSettings(): Promise<void> {
    const defaultSettings = {
      community: ['SC', 'ST', 'OBC', 'General'],
      religion: ['Christian', 'Hindu', 'Muslim', 'Sikh'],
      economic_status: ['BPL', 'EWS', 'MIG', 'HIG'],
      education: [
        'Primary',
        'Secondary',
        'Higher Secondary',
        'Graduate',
        'Post Graduate',
        'Doctorate',
        'Others',
      ],
      occupation: [
        'Private Employee',
        'Government Employee',
        'Business/Self Employed',
        'Labor/Worker',
        'Farmer',
        'Homemaker',
        'Student',
        'Retired',
        'Unemployed',
      ],
      transaction_purpose: [
        'Medical Assistance',
        'Education Support',
        'Emergency Aid',
        'Festival/Event Support',
        'Infrastructure Help',
        'Family Support',
        'Business Support',
        'Other',
      ],
    };

    for (const [category, values] of Object.entries(defaultSettings)) {
      const existingValues = await this.getCategorySettings(category);

      if (existingValues.length === 0) {
        // Add default values if category is empty
        for (const value of values) {
          await this.addCategorySetting(category, value);
        }
        console.log(`✅ Initialized default settings for ${category}`);
      } else {
        console.log(`✅ Category ${category} already has ${existingValues.length} values`);
      }
    }
  }

  /**
   * Get all categories that have settings
   */
  public async getAllCategories(): Promise<string[]> {
    try {
      const rows = await this.executeQuery('SELECT DISTINCT category FROM settings ORDER BY category');
      return rows.map(row => row.category as string);
    } catch (error) {
      console.error('Failed to get all categories:', error);
      throw error;
    }
  }
}

export default SettingsService;
