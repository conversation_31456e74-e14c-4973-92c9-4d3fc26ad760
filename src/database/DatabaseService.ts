import Database from '@tauri-apps/plugin-sql';
import { AuthService } from '../services/AuthService';

export interface DatabaseConfig {
  wasmUrl?: string;
}

export class DatabaseService {
  private static instance: DatabaseService;
  private db: Database | null = null;
  private isInitialized = false;
  private dbPath = 'sqlite:electixir.db';

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Initialize the database service
   */
  public async initialize(_config: DatabaseConfig = {}): Promise<void> {
    if (this.isInitialized) {
      console.log('✅ Database already initialized');
      return;
    }

    try {
      console.log('🔄 Initializing SQLite database...');

      // Check if we're in Tauri environment
      const isTauri = typeof window !== 'undefined' && (
        (window as any).__TAURI__ ||
        (window as any).__TAURI_METADATA__ ||
        (window as any).__TAURI_INTERNALS__ ||
        typeof (window as any).__TAURI_INVOKE__ === 'function'
      );
      console.log('🔍 Running in Tauri environment:', isTauri);
      console.log('🔍 Window object keys:', typeof window !== 'undefined' ? Object.keys(window).filter(k => k.includes('TAURI')) : 'no window');
      console.log('🔍 Database import:', Database);

      // Let's try to initialize anyway and see what happens
      if (!isTauri) {
        console.warn('⚠️ Tauri environment not detected, but attempting to initialize database anyway...');
      }

      // Initialize Tauri SQL database
      console.log('🔄 Loading SQLite database from:', this.dbPath);
      console.log('🔍 Database constructor:', Database);
      console.log('🔍 Database.load method:', Database.load);

      try {
        this.db = await Database.load(this.dbPath);
        console.log('✅ SQLite database loaded successfully');
      } catch (dbError) {
        console.error('❌ Database load error:', dbError);
        if (dbError instanceof Error) {
          console.log('🔍 Error details:', {
            name: dbError.name,
            message: dbError.message,
            stack: dbError.stack
          });
        }
        throw dbError;
      }

      // Migrate from localStorage if needed
      await this.migrateFromLocalStorage();

      // Create tables if they don't exist
      console.log('🔄 Creating database tables...');
      await this.createTables();
      console.log('✅ Database tables created');

      // Test database connection
      console.log('🔄 Testing database connection...');
      await this.testDatabaseConnection();

      // Ensure default admin user exists
      console.log('🔄 Ensuring default admin user...');
      await this.ensureDefaultAdmin();

      this.isInitialized = true;
      console.log('✅ SQLite database initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize database:', error);
      throw error;
    }
  }

  /**
   * Get the database instance
   */
  public getDatabase(): Database {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }





  /**
   * Create database tables
   */
  private async createTables(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    const createTablesSQL = `
      -- Polling Stations table
      CREATE TABLE IF NOT EXISTS polling_stations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Sections table
      CREATE TABLE IF NOT EXISTS sections (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        polling_station_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (polling_station_id) REFERENCES polling_stations(id),
        UNIQUE(name, polling_station_id)
      );

      -- Voters table (core data)
      CREATE TABLE IF NOT EXISTS voters (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        relationship_type TEXT CHECK(relationship_type IN ('Father', 'Mother', 'Husband', 'Others')),
        relationship_name TEXT,
        gender TEXT CHECK(gender IN ('Male', 'Female', 'Other')),
        birth_year INTEGER,
        epic_number TEXT UNIQUE,
        house_number TEXT,
        polling_station_id INTEGER,
        section_id INTEGER,

        -- Extended contact information (optional)
        phone TEXT,
        email TEXT,
        facebook TEXT,
        instagram TEXT,
        twitter TEXT,

        -- Voter status (optional)
        status TEXT CHECK(status IN ('Active', 'Expired', 'Shifted', 'Duplicate', 'Missing', 'Disqualified')) DEFAULT 'Active',

        -- Political information (optional)
        supporter_status TEXT CHECK(supporter_status IN ('Strong Supporter', 'Potential Supporter', 'Undecided', 'Opposed')),

        -- Demographics (optional)
        education TEXT,
        occupation TEXT,
        community TEXT,
        religion TEXT,
        economic_status TEXT,
        custom_notes TEXT,

        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (polling_station_id) REFERENCES polling_stations(id),
        FOREIGN KEY (section_id) REFERENCES sections(id)
      );

      -- Users table for authentication
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        password_hash TEXT NOT NULL,
        role TEXT CHECK(role IN ('admin', 'user')) DEFAULT 'user',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Transaction records table (with encrypted amounts)
      CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        voter_id INTEGER NOT NULL,
        date DATE NOT NULL,
        purpose TEXT NOT NULL,
        amount_encrypted TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (voter_id) REFERENCES voters(id)
      );

      -- Settings table for custom values
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category TEXT NOT NULL,
        value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(category, value)
      );

      -- Create indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_voters_epic ON voters(epic_number);
      CREATE INDEX IF NOT EXISTS idx_voters_name ON voters(name);
      CREATE INDEX IF NOT EXISTS idx_voters_polling_station ON voters(polling_station_id);
      CREATE INDEX IF NOT EXISTS idx_voters_section ON voters(section_id);
      CREATE INDEX IF NOT EXISTS idx_voters_status ON voters(status);
      CREATE INDEX IF NOT EXISTS idx_transactions_voter ON transactions(voter_id);
      CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date);
      CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
    `;

    try {
      // Remove comments and split the SQL into individual statements
      const cleanSQL = createTablesSQL
        .split('\n')
        .filter(line => !line.trim().startsWith('--'))
        .join('\n');

      const statements = cleanSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);

      console.log('🔍 Executing', statements.length, 'SQL statements');

      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        console.log(`🔄 Executing statement ${i + 1}:`, statement.substring(0, 50) + '...');
        try {
          await this.db.execute(statement);
          console.log(`✅ Statement ${i + 1} executed successfully`);
        } catch (stmtError) {
          console.error(`❌ Failed to execute statement ${i + 1}:`, statement);
          console.error('Error:', stmtError);
          throw stmtError;
        }
      }
    } catch (error) {
      console.error('Failed to create database tables:', error);
      throw error;
    }
  }

  /**
   * Test database connection
   */
  private async testDatabaseConnection(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      // Simple test query
      const result = await this.db.select('SELECT 1 as test') as any[];
      console.log('✅ Database connection test successful:', result);
    } catch (error) {
      console.error('❌ Database connection test failed:', error);
      throw error;
    }
  }

  /**
   * Migrate data from localStorage (SQL.js) to SQLite
   */
  public async migrateFromLocalStorage(): Promise<boolean> {
    try {
      const savedData = localStorage.getItem('electixir_database');
      if (!savedData) {
        console.log('No localStorage data found to migrate');
        return false;
      }

      console.log('🔄 Migrating data from localStorage to SQLite...');

      // For now, we'll just clear the localStorage since the new SQLite database
      // will be initialized with default data. In a real migration, you would:
      // 1. Parse the SQL.js database from localStorage
      // 2. Extract all data
      // 3. Insert it into the new SQLite database

      localStorage.removeItem('electixir_database');
      console.log('✅ Migration completed - localStorage cleared');

      return true;
    } catch (error) {
      console.error('❌ Migration failed:', error);
      return false;
    }
  }

  /**
   * Ensure default admin user exists
   */
  private async ensureDefaultAdmin(): Promise<void> {
    try {
      const authService = new AuthService();
      await authService.ensureDefaultAdmin();
    } catch (error) {
      console.error('Failed to ensure default admin user:', error);
      // Don't throw error here as it's not critical for database initialization
    }
  }

  /**
   * Export database as binary data for backup
   */
  public async exportDatabase(): Promise<Uint8Array> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      // For Tauri SQLite, we'll create a JSON export and convert to binary
      const exportData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        tables: {} as any
      };

      // Export all table data
      const tables = ['polling_stations', 'sections', 'voters', 'transactions', 'users', 'settings'];

      for (const table of tables) {
        try {
          const rows = await this.db.select(`SELECT * FROM ${table}`) as any[];
          exportData.tables[table] = rows;
        } catch (error) {
          console.warn(`Failed to export table ${table}:`, error);
          exportData.tables[table] = [];
        }
      }

      const jsonString = JSON.stringify(exportData);
      return new TextEncoder().encode(jsonString);
    } catch (error) {
      console.error('Failed to export database:', error);
      throw error;
    }
  }

  /**
   * Import database from binary data
   */
  public async importDatabase(data: Uint8Array): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      // Convert binary data back to JSON
      const jsonString = new TextDecoder().decode(data);
      const importData = JSON.parse(jsonString);

      console.log('🔄 Starting database import...');

      // Clear existing data first
      const tables = ['voters', 'transactions', 'settings'];
      for (const table of tables) {
        try {
          await this.db.execute(`DELETE FROM ${table}`);
          console.log(`✅ Cleared table: ${table}`);
        } catch (error) {
          console.warn(`Failed to clear table ${table}:`, error);
        }
      }

      // Import data for each table
      for (const [tableName, tableData] of Object.entries(importData.tables)) {
        if (!Array.isArray(tableData) || tableData.length === 0) {
          console.log(`⏭️ Skipping empty table: ${tableName}`);
          continue;
        }

        try {
          // Get the first row to determine columns
          const firstRow = tableData[0] as any;
          const columns = Object.keys(firstRow);
          const placeholders = columns.map(() => '?').join(', ');

          const insertSQL = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;

          // Insert each row
          for (const row of tableData) {
            const values = columns.map(col => row[col]);
            await this.db.execute(insertSQL, values);
          }

          console.log(`✅ Imported ${tableData.length} rows into ${tableName}`);
        } catch (error) {
          console.warn(`Failed to import table ${tableName}:`, error);
        }
      }

      console.log('✅ Database import completed successfully');
    } catch (error) {
      console.error('Failed to import database:', error);
      throw error;
    }
  }

  /**
   * Clear all data and reset database
   */
  public async clearDatabase(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      // Clear all tables
      const tables = ['transactions', 'voters', 'sections', 'polling_stations', 'users', 'settings'];
      for (const table of tables) {
        await this.db.execute(`DELETE FROM ${table}`);
      }

      // Ensure default admin user exists
      await this.ensureDefaultAdmin();
    } catch (error) {
      console.error('Failed to clear database:', error);
      throw error;
    }
  }

  /**
   * Check database integrity
   */
  public async checkIntegrity(): Promise<{ isValid: boolean; errors: string[] }> {
    if (!this.db) {
      return { isValid: false, errors: ['Database not initialized'] };
    }

    try {
      const result = await this.db.select('PRAGMA integrity_check') as any[];
      const errors: string[] = [];

      result.forEach((row: any) => {
        const message = row.integrity_check as string;
        if (message !== 'ok') {
          errors.push(message);
        }
      });

      return {
        isValid: errors.length === 0,
        errors,
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [error instanceof Error ? error.message : 'Unknown integrity check error'],
      };
    }
  }
}

export default DatabaseService;
