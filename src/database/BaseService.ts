import Database from '@tauri-apps/plugin-sql';
import DatabaseService from './DatabaseService';
import { handleDatabaseError, logError } from '../utils/errorHandling';

/**
 * Base class for all database services providing common functionality
 */
export abstract class BaseService {
  protected dbService: DatabaseService;

  constructor() {
    this.dbService = DatabaseService.getInstance();
  }

  /**
   * Get database instance with validation
   */
  protected getDatabase(): Database {
    const db = this.dbService.getDatabase();
    if (!db) {
      throw new Error('Database not initialized. Please initialize the database first.');
    }
    return db;
  }

  /**
   * Execute a transaction with automatic rollback on error
   */
  protected async executeTransaction<T>(
    operation: (db: Database) => Promise<T>,
    operationName: string
  ): Promise<T> {
    const db = this.getDatabase();
    let transactionStarted = false;

    try {
      // Start transaction
      await db.execute('BEGIN TRANSACTION');
      transactionStarted = true;

      // Execute operation
      const result = await operation(db);

      // Commit transaction
      await db.execute('COMMIT');
      transactionStarted = false;

      return result;
    } catch (error) {
      // Rollback transaction on error
      if (transactionStarted) {
        try {
          await db.execute('ROLLBACK');
        } catch (rollbackError) {
          logError('Transaction Rollback', rollbackError);
        }
      }

      handleDatabaseError(error, operationName);
      throw error; // Make sure to throw the error
    }
  }

  /**
   * Execute a SELECT query with proper error handling
   */
  protected async executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {
    try {
      const db = this.getDatabase();
      const result = await db.select(query, params);
      return (result || []) as T[];
    } catch (error) {
      console.error(`Query execution failed: ${query}`, error);
      handleDatabaseError(error, `Query execution: ${query}`);
      return [] as T[];
    }
  }

  /**
   * Execute a query and return single result
   */
  protected async executeQuerySingle<T = any>(query: string, params: any[] = []): Promise<T | null> {
    try {
      const results = await this.executeQuery<T>(query, params);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      handleDatabaseError(error, `Single query execution: ${query}`);
      return null;
    }
  }

  /**
   * Execute an update/delete query
   */
  protected async executeUpdate(query: string, params: any[] = []): Promise<number> {
    try {
      const db = this.getDatabase();
      const result = await db.execute(query, params);
      return result.rowsAffected || 0;
    } catch (error) {
      console.error(`Update execution failed: ${query}`, error);
      handleDatabaseError(error, `Update execution: ${query}`);
      return 0;
    }
  }

  /**
   * Execute an insert query and return the inserted ID
   */
  protected async executeInsert(query: string, params: any[] = []): Promise<number> {
    try {
      const db = this.getDatabase();
      const result = await db.execute(query, params);
      return result.lastInsertId || 0;
    } catch (error) {
      console.error(`Insert execution failed: ${query}`, error);
      handleDatabaseError(error, `Insert execution: ${query}`);
      return 0;
    }
  }

  /**
   * Check if a record exists
   */
  protected async recordExists(table: string, whereClause: string, params: any[] = []): Promise<boolean> {
    try {
      const query = `SELECT 1 FROM ${table} WHERE ${whereClause} LIMIT 1`;
      const result = await this.executeQuerySingle(query, params);
      return result !== null;
    } catch (error) {
      handleDatabaseError(error, `Record existence check: ${table}`);
      return false;
    }
  }

  /**
   * Get count of records
   */
  protected async getRecordCount(table: string, whereClause: string = '1=1', params: any[] = []): Promise<number> {
    try {
      const query = `SELECT COUNT(*) as count FROM ${table} WHERE ${whereClause}`;
      const result = await this.executeQuerySingle<{ count: number }>(query, params);
      return result?.count || 0;
    } catch (error) {
      handleDatabaseError(error, `Record count: ${table}`);
      return 0;
    }
  }

  /**
   * Validate required fields
   */
  protected validateRequiredFields(data: Record<string, any>, requiredFields: string[]): void {
    const missingFields = requiredFields.filter(field => !data[field]);
    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }
  }

  /**
   * Sanitize and validate data before database operations
   */
  protected sanitizeData<T extends Record<string, any>>(data: T): T {
    const sanitized = { ...data } as any;

    // Sanitize string fields
    Object.keys(sanitized).forEach(key => {
      if (typeof sanitized[key] === 'string') {
        sanitized[key] = sanitized[key].trim();
        // Remove any null bytes that could cause issues
        sanitized[key] = sanitized[key].replace(/\0/g, '');
      }
    });

    return sanitized as T;
  }

  /**
   * Log operation for audit trail
   */
  protected logOperation(operation: string, details: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${this.constructor.name}] ${operation}:`, details);
    }
  }
}
