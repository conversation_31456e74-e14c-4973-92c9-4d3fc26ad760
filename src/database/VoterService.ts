import { BaseService } from './BaseService';
import { validateEpicNumber, validateEmail, validatePhone } from '../utils/validation';
import { VOTER_STATUS, SUPPORTER_STATUS, GENDER, RELATIONSHIP_TYPE } from '../utils/constants';

export interface VoterData {
  id?: number;
  name: string;
  relationship_type?: 'Father' | 'Mother' | 'Husband' | 'Others';
  relationship_name?: string;
  gender: 'Male' | 'Female' | 'Other';
  birth_year?: number;
  epic_number: string;
  house_number?: string;
  polling_station?: string;
  section?: string;

  // Extended contact information
  phone?: string;
  email?: string;
  facebook?: string;
  instagram?: string;
  twitter?: string;

  // Voter status
  status?: 'Active' | 'Expired' | 'Shifted' | 'Duplicate' | 'Missing' | 'Disqualified';

  // Political information
  supporter_status?: 'Strong Supporter' | 'Potential Supporter' | 'Undecided' | 'Opposed';

  // Demographics
  education?: string;
  occupation?: string;
  community?: string;
  religion?: string;
  economic_status?: string;
  custom_notes?: string;
}

export interface VoterFilters {
  searchTerm?: string;
  gender?: string;
  status?: string;
  polling_station?: string;
  section?: string;
  community?: string;
  religion?: string;
  economic_status?: string;
  supporter_status?: string;
  ageFrom?: number;
  ageTo?: number;
}

export class VoterService extends BaseService {
  constructor() {
    super();
  }

  /**
   * Validate voter data before database operations
   */
  private validateVoterData(voterData: VoterData): void {
    // Validate required fields
    this.validateRequiredFields(voterData, ['name', 'epic_number', 'gender']);

    // Validate EPIC number format
    if (!validateEpicNumber(voterData.epic_number)) {
      throw new Error('Invalid EPIC number format');
    }

    // Validate email if provided
    if (voterData.email && !validateEmail(voterData.email)) {
      throw new Error('Invalid email format');
    }

    // Validate phone if provided
    if (voterData.phone && !validatePhone(voterData.phone)) {
      throw new Error('Invalid phone number format');
    }

    // Validate enum values
    if (voterData.status && !Object.values(VOTER_STATUS).includes(voterData.status as any)) {
      throw new Error(`Invalid voter status: ${voterData.status}`);
    }

    if (
      voterData.supporter_status &&
      !Object.values(SUPPORTER_STATUS).includes(voterData.supporter_status as any)
    ) {
      throw new Error(`Invalid supporter status: ${voterData.supporter_status}`);
    }

    if (!Object.values(GENDER).includes(voterData.gender as any)) {
      throw new Error(`Invalid gender: ${voterData.gender}`);
    }

    if (
      voterData.relationship_type &&
      !Object.values(RELATIONSHIP_TYPE).includes(voterData.relationship_type as any)
    ) {
      throw new Error(`Invalid relationship type: ${voterData.relationship_type}`);
    }
  }

  /**
   * Add a new voter or update existing one based on epic_number
   */
  public async upsertVoter(voterData: VoterData): Promise<{ id: number; isNew: boolean }> {
    try {
      // Validate and sanitize data
      this.validateVoterData(voterData);
      const sanitizedData = this.sanitizeData(voterData);

      // Check if voter with this epic_number already exists
      const existingVoter = await this.executeQuerySingle<{ id: number }>(
        'SELECT id FROM voters WHERE epic_number = ?',
        [sanitizedData.epic_number]
      );

      if (existingVoter) {
        // Update existing voter
        await this.updateVoter(existingVoter.id, sanitizedData);
        this.logOperation('Voter Updated', {
          id: existingVoter.id,
          epic: sanitizedData.epic_number,
        });
        return { id: existingVoter.id, isNew: false };
      } else {
        // Add new voter
        const newId = await this.addVoter(sanitizedData);
        this.logOperation('Voter Added', { id: newId, epic: sanitizedData.epic_number });
        return { id: newId, isNew: true };
      }
    } catch (error) {
      console.error('Failed to upsert voter:', error);
      throw error;
    }
  }

  /**
   * Add a new voter
   */
  public async addVoter(voterData: VoterData): Promise<number> {
    try {
      // First, ensure polling station exists
      let pollingStationId: number | null = null;
      if (voterData.polling_station) {
        pollingStationId = await this.ensurePollingStationExists(voterData.polling_station);
      }

      // Then, ensure section exists
      let sectionId: number | null = null;
      if (voterData.section && pollingStationId) {
        sectionId = await this.ensureSectionExists(voterData.section, pollingStationId);
      }

      const voterId = await this.executeInsert(`
        INSERT INTO voters (
          name, relationship_type, relationship_name, gender, birth_year, epic_number,
          house_number, polling_station_id, section_id, phone, email, facebook,
          instagram, twitter, status, supporter_status, education, occupation,
          community, religion, economic_status, custom_notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        voterData.name,
        voterData.relationship_type || null,
        voterData.relationship_name || null,
        voterData.gender,
        voterData.birth_year || null,
        voterData.epic_number,
        voterData.house_number || null,
        pollingStationId,
        sectionId,
        voterData.phone || null,
        voterData.email || null,
        voterData.facebook || null,
        voterData.instagram || null,
        voterData.twitter || null,
        voterData.status || 'Active',
        voterData.supporter_status || null,
        voterData.education || null,
        voterData.occupation || null,
        voterData.community || null,
        voterData.religion || null,
        voterData.economic_status || null,
        voterData.custom_notes || null,
      ]);

      return voterId;
    } catch (error) {
      console.error('Failed to add voter:', error);
      throw error;
    }
  }

  /**
   * Get all voters with optional filters
   */
  public async getVoters(filters: VoterFilters = {}): Promise<VoterData[]> {
    try {
      let sql = `
        SELECT
          v.*,
          ps.name as polling_station,
          s.name as section
        FROM voters v
        LEFT JOIN polling_stations ps ON v.polling_station_id = ps.id
        LEFT JOIN sections s ON v.section_id = s.id
        WHERE 1=1
      `;

      const params: any[] = [];

      // Apply filters
      if (filters.searchTerm) {
        sql += ` AND (v.name LIKE ? OR v.epic_number LIKE ?)`;
        params.push(`%${filters.searchTerm}%`, `%${filters.searchTerm}%`);
      }

      if (filters.gender && filters.gender !== 'All') {
        sql += ` AND v.gender = ?`;
        params.push(filters.gender);
      }

      if (filters.status && filters.status !== 'All') {
        sql += ` AND v.status = ?`;
        params.push(filters.status);
      }

      if (filters.polling_station && filters.polling_station !== 'All') {
        sql += ` AND ps.name = ?`;
        params.push(filters.polling_station);
      }

      if (filters.section && filters.section !== 'All') {
        sql += ` AND s.name = ?`;
        params.push(filters.section);
      }

      if (filters.community && filters.community !== 'All') {
        sql += ` AND v.community = ?`;
        params.push(filters.community);
      }

      if (filters.religion && filters.religion !== 'All') {
        sql += ` AND v.religion = ?`;
        params.push(filters.religion);
      }

      if (filters.economic_status && filters.economic_status !== 'All') {
        sql += ` AND v.economic_status = ?`;
        params.push(filters.economic_status);
      }

      if (filters.supporter_status && filters.supporter_status !== 'All') {
        sql += ` AND v.supporter_status = ?`;
        params.push(filters.supporter_status);
      }

      // Age filters
      if (filters.ageFrom !== undefined) {
        const currentYear = new Date().getFullYear();
        const maxBirthYear = currentYear - filters.ageFrom;
        sql += ` AND v.birth_year <= ?`;
        params.push(maxBirthYear);
      }

      if (filters.ageTo !== undefined) {
        const currentYear = new Date().getFullYear();
        const minBirthYear = currentYear - filters.ageTo;
        sql += ` AND v.birth_year >= ?`;
        params.push(minBirthYear);
      }

      sql += ` ORDER BY v.name`;

      const rows = await this.executeQuery(sql, params);

      const mappedRows = rows.map(row => ({
        id: row.id as number,
        name: row.name as string,
        relationship_type: row.relationship_type as any,
        relationship_name: row.relationship_name as string,
        gender: row.gender as any,
        birth_year: row.birth_year as number,
        epic_number: row.epic_number as string,
        house_number: row.house_number as string,
        polling_station: row.polling_station as string,
        section: row.section as string,
        phone: row.phone as string,
        email: row.email as string,
        facebook: row.facebook as string,
        instagram: row.instagram as string,
        twitter: row.twitter as string,
        status: row.status as any,
        supporter_status: row.supporter_status as any,
        education: row.education as string,
        occupation: row.occupation as string,
        community: row.community as string,
        religion: row.religion as string,
        economic_status: row.economic_status as string,
        custom_notes: row.custom_notes as string,
      }));

      return mappedRows;
    } catch (error) {
      console.error('Failed to get voters:', error);
      throw error;
    }
  }

  /**
   * Get voter by ID
   */
  public async getVoterById(id: number): Promise<VoterData | null> {
    const voters = await this.getVoters();
    return voters.find(v => v.id === id) || null;
  }

  /**
   * Update voter
   */
  public async updateVoter(id: number, voterData: Partial<VoterData>): Promise<void> {
    try {
      // Handle polling station and section updates - only use existing ones
      let pollingStationId: number | null = null;
      let sectionId: number | null = null;

      if (voterData.polling_station) {
        pollingStationId = await this.getPollingStationId(voterData.polling_station);
      }

      if (voterData.section && pollingStationId) {
        sectionId = await this.getSectionId(voterData.section, pollingStationId);
      }

      const updateFields: string[] = [];
      const params: any[] = [];

      // Build dynamic update query - exclude system fields
      Object.entries(voterData).forEach(([key, value]) => {
        if (key !== 'id' && key !== 'polling_station' && key !== 'section') {
          updateFields.push(`${key} = ?`);
          params.push(value === undefined ? null : value);
        }
      });

      // Add polling station and section IDs if they were processed
      if (voterData.polling_station !== undefined) {
        updateFields.push('polling_station_id = ?');
        params.push(pollingStationId);
      }

      if (voterData.section !== undefined) {
        updateFields.push('section_id = ?');
        params.push(sectionId);
      }

      // Add updated_at timestamp
      updateFields.push('updated_at = CURRENT_TIMESTAMP');

      if (updateFields.length === 0) {
        return;
      }

      params.push(id);

      const sql = `UPDATE voters SET ${updateFields.join(', ')} WHERE id = ?`;

      const rowsAffected = await this.executeUpdate(sql, params);

      if (rowsAffected === 0) {
        throw new Error(`No voter found with ID ${id} or no changes were made`);
      }
    } catch (error) {
      console.error('❌ Failed to update voter:', error);
      throw error;
    }
  }

  /**
   * Delete voter
   */
  public async deleteVoter(id: number): Promise<void> {
    try {
      const rowsAffected = await this.executeUpdate('DELETE FROM voters WHERE id = ?', [id]);

      if (rowsAffected === 0) {
        throw new Error(`No voter found with ID ${id}`);
      }
    } catch (error) {
      console.error('Failed to delete voter:', error);
      throw error;
    }
  }

  /**
   * Get polling station ID if exists, return null if not
   */
  private async getPollingStationId(name: string): Promise<number | null> {
    try {
      const result = await this.executeQuerySingle<{ id: number }>(
        'SELECT id FROM polling_stations WHERE name = ?',
        [name]
      );
      return result ? result.id : null;
    } catch (error) {
      console.error('Failed to get polling station ID:', error);
      return null;
    }
  }

  /**
   * Get section ID if exists, return null if not
   */
  private async getSectionId(name: string, pollingStationId: number): Promise<number | null> {
    try {
      const result = await this.executeQuerySingle<{ id: number }>(
        'SELECT id FROM sections WHERE name = ? AND polling_station_id = ?',
        [name, pollingStationId]
      );
      return result ? result.id : null;
    } catch (error) {
      console.error('Failed to get section ID:', error);
      return null;
    }
  }

  /**
   * Ensure polling station exists, create if not (used during import)
   */
  private async ensurePollingStationExists(name: string): Promise<number> {
    try {
      // Check if exists
      const existing = await this.executeQuerySingle<{ id: number }>(
        'SELECT id FROM polling_stations WHERE name = ?',
        [name]
      );

      if (existing) {
        return existing.id;
      }

      // Create new
      return await this.executeInsert('INSERT INTO polling_stations (name) VALUES (?)', [name]);
    } catch (error) {
      console.error('Failed to ensure polling station exists:', error);
      throw error;
    }
  }

  /**
   * Ensure section exists, create if not (used during import)
   */
  private async ensureSectionExists(name: string, pollingStationId: number): Promise<number> {
    try {
      // Check if exists
      const existing = await this.executeQuerySingle<{ id: number }>(
        'SELECT id FROM sections WHERE name = ? AND polling_station_id = ?',
        [name, pollingStationId]
      );

      if (existing) {
        return existing.id;
      }

      // Create new
      return await this.executeInsert(
        'INSERT INTO sections (name, polling_station_id) VALUES (?, ?)',
        [name, pollingStationId]
      );
    } catch (error) {
      console.error('Failed to ensure section exists:', error);
      throw error;
    }
  }



  /**
   * Get all polling stations
   */
  public async getPollingStations(): Promise<string[]> {
    try {
      const rows = await this.executeQuery('SELECT name FROM polling_stations ORDER BY name');
      return rows.map(row => row.name as string);
    } catch (error) {
      console.error('Failed to get polling stations:', error);
      throw error;
    }
  }

  /**
   * Get all sections for a polling station
   */
  public async getSectionsForPollingStation(pollingStationName: string): Promise<string[]> {
    try {
      const sql = `
        SELECT s.name
        FROM sections s
        JOIN polling_stations ps ON s.polling_station_id = ps.id
        WHERE ps.name = ?
        ORDER BY s.name
      `;
      const rows = await this.executeQuery(sql, [pollingStationName]);
      return rows.map(row => row.name as string);
    } catch (error) {
      console.error('Failed to get sections for polling station:', error);
      throw error;
    }
  }

  /**
   * Get households grouped by relationship_name and house_number
   */
  public async getHouseholds(filters: VoterFilters = {}): Promise<Household[]> {
    const voters = await this.getVoters(filters);
    const householdMap = new Map<string, VoterData[]>();

    // Group voters by household key (relationship_name + house_number)
    voters.forEach(voter => {
      if (voter.relationship_name && voter.house_number) {
        const householdKey = `${voter.relationship_name.trim()}_${voter.house_number.trim()}`;
        if (!householdMap.has(householdKey)) {
          householdMap.set(householdKey, []);
        }
        householdMap.get(householdKey)!.push(voter);
      }
    });

    // Convert to household objects
    const households: Household[] = [];
    householdMap.forEach((members, key) => {
      if (members.length > 1) {
        // Only include households with multiple members
        const [relationshipName, houseNumber] = key.split('_');
        households.push({
          id: key,
          relationship_name: relationshipName,
          house_number: houseNumber,
          members: members,
          memberCount: members.length,
          polling_station: members[0].polling_station,
          section: members[0].section,
        });
      }
    });

    // Sort by member count (largest households first)
    return households.sort((a, b) => b.memberCount - a.memberCount);
  }
}

export interface Household {
  id: string;
  relationship_name: string;
  house_number: string;
  members: VoterData[];
  memberCount: number;
  polling_station?: string;
  section?: string;
}

export default VoterService;
