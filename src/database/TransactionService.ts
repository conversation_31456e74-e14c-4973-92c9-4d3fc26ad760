import { BaseService } from './BaseService';
import { validateTransaction } from '../utils/validation';
import { EncryptionService } from '../utils/encryption';

export interface Transaction {
  id?: number;
  voter_id: number;
  date: string; // DD-MM-YYYY format
  purpose: string;
  amount: number; // Integer, no decimals
  created_at?: string;
}

export class TransactionService extends BaseService {
  constructor() {
    super();
  }

  /**
   * Get all transactions for a voter
   */
  public async getTransactionsForVoter(voterId: number): Promise<Transaction[]> {
    try {
      const rows = await this.executeQuery('SELECT * FROM transactions WHERE voter_id = ? ORDER BY date DESC', [voterId]);

      return rows.map(row => ({
        id: row.id as number,
        voter_id: row.voter_id as number,
        date: row.date as string,
        purpose: row.purpose as string,
        amount: EncryptionService.decryptAmount(row.amount_encrypted as string), // Decrypt amount
        created_at: row.created_at as string,
      }));
    } catch (error) {
      console.error('Failed to get transactions for voter:', error);
      throw error;
    }
  }

  /**
   * Add a new transaction
   */
  public async addTransaction(
    transaction: Omit<Transaction, 'id' | 'created_at'>
  ): Promise<number> {
    // Validate transaction data
    const validation = validateTransaction(transaction);
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '));
    }

    // Sanitize data
    const sanitizedTransaction = this.sanitizeData(transaction);

    try {
      // Encrypt the amount before storing
      const encryptedAmount = EncryptionService.encryptAmount(sanitizedTransaction.amount);

      const insertId = await this.executeInsert(
        'INSERT INTO transactions (voter_id, date, purpose, amount_encrypted) VALUES (?, ?, ?, ?)',
        [
          sanitizedTransaction.voter_id,
          sanitizedTransaction.date,
          sanitizedTransaction.purpose,
          encryptedAmount, // Store encrypted amount
        ]
      );

      this.logOperation('Transaction Added', {
        id: insertId,
        voter_id: sanitizedTransaction.voter_id,
      });
      return insertId;
    } catch (error) {
      console.error('Failed to add transaction:', error);
      throw error;
    }
  }

  /**
   * Update a transaction
   */
  public async updateTransaction(
    id: number,
    transaction: Omit<Transaction, 'id' | 'voter_id' | 'created_at'>
  ): Promise<void> {
    try {
      // Validate transaction data (excluding voter_id for updates)
      const validation = validateTransaction({ ...transaction, voter_id: 1 }); // Dummy voter_id for validation
      if (!validation.isValid) {
        throw new Error(validation.errors.filter(e => !e.includes('Voter ID')).join(', '));
      }

      // Encrypt the amount before updating
      const encryptedAmount = EncryptionService.encryptAmount(transaction.amount);

      await this.executeUpdate(`
        UPDATE transactions
        SET date = ?, purpose = ?, amount_encrypted = ?
        WHERE id = ?
      `, [transaction.date, transaction.purpose.trim(), encryptedAmount, id]);

    } catch (error) {
      console.error('Failed to update transaction:', error);
      throw error;
    }
  }

  /**
   * Delete a transaction
   */
  public async deleteTransaction(id: number): Promise<void> {
    try {
      await this.executeUpdate('DELETE FROM transactions WHERE id = ?', [id]);
    } catch (error) {
      console.error('Failed to delete transaction:', error);
      throw error;
    }
  }

  /**
   * Get total amount for a voter
   */
  public async getTotalForVoter(voterId: number): Promise<number> {
    try {
      // Get all encrypted amounts and decrypt them to calculate total
      const rows = await this.executeQuery('SELECT amount_encrypted FROM transactions WHERE voter_id = ?', [voterId]);

      let total = 0;
      for (const row of rows) {
        const decryptedAmount = EncryptionService.decryptAmount(row.amount_encrypted as string);
        total += decryptedAmount;
      }

      return total;
    } catch (error) {
      console.error('Failed to get total for voter:', error);
      throw error;
    }
  }

  /**
   * Get all transactions
   */
  public async getAllTransactions(): Promise<Transaction[]> {
    try {
      const rows = await this.executeQuery('SELECT * FROM transactions ORDER BY date DESC');

      return rows.map(row => ({
        id: row.id as number,
        voter_id: row.voter_id as number,
        date: row.date as string,
        purpose: row.purpose as string,
        amount: EncryptionService.decryptAmount(row.amount_encrypted as string), // Decrypt amount
        created_at: row.created_at as string,
      }));
    } catch (error) {
      console.error('Failed to get all transactions:', error);
      throw error;
    }
  }
}

export default TransactionService;
