import { useState, useEffect } from 'react';
import { useApp } from '../context/AppContext';
import TransactionService from '../database/TransactionService';
import { formatCurrency } from '../components/charts/ChartConfig';

interface VoterTransactionData {
  [voterId: number]: {
    totalAmount: number;
    transactionCount: number;
    formattedAmount: string;
  };
}

export function useVoterTransactions() {
  const { state } = useApp();
  const [voterTransactions, setVoterTransactions] = useState<VoterTransactionData>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Only load transaction data if database is initialized
    if (!state.isDatabaseInitialized) {
      return;
    }

    const loadTransactionData = async () => {
      setIsLoading(true);
      try {
        const transactionService = new TransactionService();
        const allTransactions = await transactionService.getAllTransactions();

        // Group transactions by voter_id and calculate totals
        const transactionMap: VoterTransactionData = {};

        allTransactions.forEach(transaction => {
          const voterId = transaction.voter_id;

          if (!transactionMap[voterId]) {
            transactionMap[voterId] = {
              totalAmount: 0,
              transactionCount: 0,
              formattedAmount: '₹0',
            };
          }

          transactionMap[voterId].totalAmount += transaction.amount;
          transactionMap[voterId].transactionCount += 1;
        });

        // Format amounts for display
        Object.keys(transactionMap).forEach(voterIdStr => {
          const voterId = parseInt(voterIdStr);
          const data = transactionMap[voterId];
          data.formattedAmount = formatCurrency(data.totalAmount);
        });

        setVoterTransactions(transactionMap);
      } catch (error) {
        console.error('Failed to load transaction data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTransactionData();
  }, [state.isDatabaseInitialized]);

  const getVoterTransactionData = (voterId: number | undefined) => {
    if (!voterId || !voterTransactions[voterId]) {
      return {
        totalAmount: 0,
        transactionCount: 0,
        formattedAmount: '₹0',
      };
    }
    return voterTransactions[voterId];
  };

  return {
    voterTransactions,
    getVoterTransactionData,
    isLoading,
  };
}
