import { useState, useCallback } from 'react';

interface DialogState {
  isOpen: boolean;
  title: string;
  message: string;
  variant?: 'default' | 'danger' | 'success' | 'error' | 'info';
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
}

export function useDialog() {
  const [confirmDialog, setConfirmDialog] = useState<DialogState>({
    isOpen: false,
    title: '',
    message: '',
    variant: 'default',
  });

  const [alertDialog, setAlertDialog] = useState<DialogState>({
    isOpen: false,
    title: '',
    message: '',
    variant: 'info',
  });

  // Confirm dialog methods
  const showConfirm = useCallback(
    (options: {
      title: string;
      message: string;
      onConfirm: () => void;
      variant?: 'default' | 'danger';
      confirmText?: string;
      cancelText?: string;
    }) => {
      setConfirmDialog({
        isOpen: true,
        title: options.title,
        message: options.message,
        variant: options.variant || 'default',
        confirmText: options.confirmText || 'Confirm',
        cancelText: options.cancelText || 'Cancel',
        onConfirm: options.onConfirm,
      });
    },
    []
  );

  const hideConfirm = useCallback(() => {
    setConfirmDialog(prev => ({ ...prev, isOpen: false }));
  }, []);

  // Alert dialog methods
  const showAlert = useCallback(
    (options: { title: string; message: string; variant?: 'success' | 'error' | 'info' }) => {
      setAlertDialog({
        isOpen: true,
        title: options.title,
        message: options.message,
        variant: options.variant || 'info',
      });
    },
    []
  );

  const hideAlert = useCallback(() => {
    setAlertDialog(prev => ({ ...prev, isOpen: false }));
  }, []);

  // Convenience methods
  const showSuccess = useCallback(
    (title: string, message: string) => {
      showAlert({ title, message, variant: 'success' });
    },
    [showAlert]
  );

  const showError = useCallback(
    (title: string, message: string) => {
      showAlert({ title, message, variant: 'error' });
    },
    [showAlert]
  );

  const showDeleteConfirm = useCallback(
    (itemName: string, onConfirm: () => void) => {
      showConfirm({
        title: 'Delete Confirmation',
        message: `Are you sure you want to permanently delete "${itemName}"?\n\nThis action cannot be undone.`,
        variant: 'danger',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        onConfirm,
      });
    },
    [showConfirm]
  );

  return {
    // Dialog states
    confirmDialog,
    alertDialog,

    // Dialog controls
    showConfirm,
    hideConfirm,
    showAlert,
    hideAlert,

    // Convenience methods
    showSuccess,
    showError,
    showDeleteConfirm,
  };
}
