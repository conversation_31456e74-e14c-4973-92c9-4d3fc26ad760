import { useMemo } from 'react';
import { useVoterTransactions } from './useVoterTransactions';
import { VoterData } from '../database/VoterService';

export interface EnrichedVoter extends VoterData {
  // Computed fields for display
  age?: number;
  epic: string;
  pollingStation: string;
  economicStatus: string;
  fatherName: string;
  qualification: string;
  
  // New enriched fields
  affiliation: string;
  payee: string;
}

export function useEnrichedVoters(voters: VoterData[]): EnrichedVoter[] {
  const { getVoterTransactionData } = useVoterTransactions();

  const enrichedVoters = useMemo(() => {
    return voters.map(voter => {
      // Get transaction data for this voter
      const transactionData = getVoterTransactionData(voter.id);
      
      // Calculate age
      const age = voter.birth_year ? new Date().getFullYear() - voter.birth_year : undefined;
      
      // Format affiliation (supporter status)
      const affiliation = voter.supporter_status || 'Unknown';
      
      // Format payee (transaction amount)
      const payee = transactionData.totalAmount > 0 ? transactionData.formattedAmount : '-';

      return {
        ...voter,
        // Existing computed fields
        age,
        epic: voter.epic_number || '',
        pollingStation: voter.polling_station || '',
        economicStatus: voter.economic_status || '',
        fatherName: voter.relationship_type === 'Father' ? voter.relationship_name || '' : '',
        qualification: voter.education || '',
        
        // New enriched fields
        affiliation,
        payee,
      } as EnrichedVoter;
    });
  }, [voters, getVoterTransactionData]);

  return enrichedVoters;
}
