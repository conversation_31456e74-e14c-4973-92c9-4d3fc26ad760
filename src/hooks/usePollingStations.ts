import { useState, useCallback, useEffect, useRef } from 'react';
import { Voter } from '../context/AppContext';

export interface PollingStationSection {
  id: string;
  name: string;
  isSelected: boolean;
  voterCount: number;
}

export interface PollingStation {
  id: string;
  name: string;
  isOpen: boolean;
  isSelected: boolean;
  sections: PollingStationSection[];
  voterCount: number;
}

export function usePollingStations(voters: Voter[], dispatch: any) {
  const [pollingStations, setPollingStations] = useState<PollingStation[]>([]);
  const previousPollingStationsRef = useRef<PollingStation[]>([]);

  // Build polling stations from voter data
  useEffect(() => {
    console.log('🔄 usePollingStations: voters changed, rebuilding stations. Voters count:', voters.length);

    const stationMap = new Map<string, { sections: Map<string, number>; totalVoters: number }>();

    // Group voters by polling station and section
    voters.forEach(voter => {
      const stationName = voter.polling_station || voter.pollingStation || 'Unassigned';
      const sectionName = voter.section || 'Unassigned';

      if (!stationMap.has(stationName)) {
        stationMap.set(stationName, { sections: new Map(), totalVoters: 0 });
      }

      const station = stationMap.get(stationName)!;
      station.totalVoters++;

      const currentSectionCount = station.sections.get(sectionName) || 0;
      station.sections.set(sectionName, currentSectionCount + 1);
    });

    // Create a map of existing UI state to preserve it
    const existingStationState = new Map<string, { isOpen: boolean; isSelected: boolean }>();
    const existingSectionState = new Map<string, { isSelected: boolean }>();

    previousPollingStationsRef.current.forEach(station => {
      existingStationState.set(station.name, {
        isOpen: station.isOpen,
        isSelected: station.isSelected
      });

      station.sections.forEach(section => {
        existingSectionState.set(`${station.name}_${section.name}`, {
          isSelected: section.isSelected
        });
      });
    });

    // Convert to PollingStation array, preserving existing UI state
    const newPollingStations: PollingStation[] = Array.from(stationMap.entries()).map(
      ([stationName, stationData], index) => {
        const existingStation = existingStationState.get(stationName);

        const sections: PollingStationSection[] = Array.from(stationData.sections.entries()).map(
          ([sectionName, voterCount], sectionIndex) => {
            const existingSection = existingSectionState.get(`${stationName}_${sectionName}`);

            return {
              id: `station_${index}_section_${sectionIndex}_${sectionName.replace(/\s+/g, '_')}`,
              name: sectionName,
              isSelected: existingSection ? existingSection.isSelected : true,
              voterCount,
            };
          }
        );

        return {
          id: `station_${index}_${stationName.replace(/\s+/g, '_')}`,
          name: stationName,
          isOpen: existingStation ? existingStation.isOpen : false,
          isSelected: existingStation ? existingStation.isSelected : true,
          sections: sections.sort((a, b) =>
            a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' })
          ),
          voterCount: stationData.totalVoters,
        };
      }
    );

    // Sort stations by name
    newPollingStations.sort((a, b) =>
      a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' })
    );

    // Update the ref with the new state before setting it
    previousPollingStationsRef.current = newPollingStations;
    setPollingStations(newPollingStations);
  }, [voters]);

  // Helper function to update AppContext with current selections
  const updateAppContextSelections = useCallback((stations: PollingStation[]) => {
    const selectedStations = stations.filter(s => s.isSelected).map(s => s.name);
    const selectedSections: Record<string, string[]> = {};
    stations.forEach(station => {
      if (station.isSelected) {
        selectedSections[station.name] = station.sections
          .filter(sec => sec.isSelected)
          .map(sec => sec.name);
      }
    });

    dispatch({ type: 'UPDATE_SELECTED_POLLING_STATIONS', payload: selectedStations });
    dispatch({ type: 'UPDATE_SELECTED_SECTIONS', payload: selectedSections });
  }, [dispatch]);

  // Initialize AppContext selections only once when polling stations are first built
  const hasInitializedRef = useRef(false);
  useEffect(() => {
    if (pollingStations.length > 0 && !hasInitializedRef.current) {
      hasInitializedRef.current = true;
      // Use setTimeout to ensure this runs after render is complete
      setTimeout(() => {
        updateAppContextSelections(pollingStations);
      }, 0);
    }
  }, [pollingStations, updateAppContextSelections]);

  const toggleStationOpen = useCallback((stationId: string) => {
    setPollingStations(prev =>
      prev.map(station =>
        station.id === stationId ? { ...station, isOpen: !station.isOpen } : station
      )
    );
  }, []);

  const toggleStationSelected = useCallback((stationId: string) => {
    setPollingStations(prev => {
      const newPollingStations = prev.map(station =>
        station.id === stationId
          ? {
              ...station,
              isSelected: !station.isSelected,
              sections: station.sections.map(section => ({
                ...section,
                isSelected: !station.isSelected,
              })),
            }
          : station
      );

      updateAppContextSelections(newPollingStations);
      return newPollingStations;
    });
  }, [updateAppContextSelections]);

  const toggleSectionSelected = useCallback((stationId: string, sectionId: string) => {
    setPollingStations(prev => {
      const newPollingStations = prev.map(station =>
        station.id === stationId
          ? {
              ...station,
              sections: station.sections.map(section =>
                section.id === sectionId
                  ? { ...section, isSelected: !section.isSelected }
                  : section
              ),
            }
          : station
      );

      updateAppContextSelections(newPollingStations);
      return newPollingStations;
    });
  }, [updateAppContextSelections]);

  const getSelectedCount = useCallback(() => {
    return pollingStations.filter(station => station.isSelected).length;
  }, [pollingStations]);

  const getSelectedSectionsCount = useCallback(() => {
    return pollingStations.reduce(
      (total, station) => total + station.sections.filter(section => section.isSelected).length,
      0
    );
  }, [pollingStations]);

  const toggleAll = useCallback((select: boolean) => {
    setPollingStations(prev => {
      const newPollingStations = prev.map(station => ({
        ...station,
        isSelected: select,
        sections: station.sections.map(section => ({
          ...section,
          isSelected: select,
        })),
      }));

      updateAppContextSelections(newPollingStations);
      return newPollingStations;
    });
  }, [updateAppContextSelections]);

  return {
    pollingStations,
    toggleStationOpen,
    toggleStationSelected,
    toggleSectionSelected,
    getSelectedCount,
    getSelectedSectionsCount,
    totalStations: pollingStations.length,
    toggleAll,
  };
}
