import { useCallback } from 'react';
import { useApp } from '../context/AppContext';
import { useFilteredVoters } from './useFilteredVoters';
import { useEnrichedVoters } from './useEnrichedVoters';
import { ExportService } from '../services/ExportService';
import { TauriExportService } from '../services/TauriExportService';

export function useExport() {
  const { state } = useApp();
  const baseFilteredVoters = useFilteredVoters(state.voters, state.filters);
  const enrichedVoters = useEnrichedVoters(baseFilteredVoters);

  // Check if running in Tauri
  const isTauri = typeof window !== 'undefined' && window.__TAURI__;

  const exportToPDF = useCallback(async () => {
    try {
      // Convert column visibility to export columns
      const columns = [
        { key: 'name', label: 'NAME', visible: state.columnVisibility.name },
        { key: 'age', label: 'AGE', visible: state.columnVisibility.age },
        { key: 'gender', label: 'GENDER', visible: state.columnVisibility.gender },
        { key: 'epic', label: 'EPIC NUMBER', visible: state.columnVisibility.epic },
        {
          key: 'pollingStation',
          label: 'POLLING STATION',
          visible: state.columnVisibility.pollingStation,
        },
        { key: 'affiliation', label: 'AFFILIATION', visible: state.columnVisibility.affiliation },
        { key: 'payee', label: 'PAYEE', visible: state.columnVisibility.payee },
        { key: 'status', label: 'STATUS', visible: state.columnVisibility.status },
      ];

      const exportOptions = {
        columns,
        data: enrichedVoters,
        title: 'Voter Database Export',
        filters: state.filters,
        filename: `voters_export_${new Date().toISOString().split('T')[0]}.pdf`,
      };

      if (isTauri) {
        await TauriExportService.exportToPDF(exportOptions);
      } else {
        ExportService.exportToPDF(exportOptions);
      }
    } catch (error) {
      throw new Error(
        `PDF export failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }, [state.columnVisibility, state.filters, enrichedVoters, isTauri]);

  const exportToCSV = useCallback(async () => {
    try {
      // Convert column visibility to export columns
      const columns = [
        { key: 'name', label: 'NAME', visible: state.columnVisibility.name },
        { key: 'age', label: 'AGE', visible: state.columnVisibility.age },
        { key: 'gender', label: 'GENDER', visible: state.columnVisibility.gender },
        { key: 'epic', label: 'EPIC NUMBER', visible: state.columnVisibility.epic },
        {
          key: 'pollingStation',
          label: 'POLLING STATION',
          visible: state.columnVisibility.pollingStation,
        },
        { key: 'affiliation', label: 'AFFILIATION', visible: state.columnVisibility.affiliation },
        { key: 'payee', label: 'PAYEE', visible: state.columnVisibility.payee },
        { key: 'status', label: 'STATUS', visible: state.columnVisibility.status },
      ];

      const exportOptions = {
        columns,
        data: enrichedVoters,
        filename: `voters_export_${new Date().toISOString().split('T')[0]}.csv`,
      };

      if (isTauri) {
        await TauriExportService.exportToCSV(exportOptions);
      } else {
        ExportService.exportToCSV(exportOptions);
      }
    } catch (error) {
      throw new Error(
        `CSV export failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }, [state.columnVisibility, enrichedVoters, isTauri]);

  return {
    exportToPDF,
    exportToCSV,
    recordCount: enrichedVoters.length,
    visibleColumnCount: Object.values(state.columnVisibility).filter(Boolean).length,
  };
}
