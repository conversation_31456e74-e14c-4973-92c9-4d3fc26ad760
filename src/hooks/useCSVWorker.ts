import { useRef, useCallback } from 'react';

interface CSVWorkerHookResult {
  parseCSV: (csvContent: string, options?: any) => Promise<any[]>;
  validateCSV: (csvContent: string) => Promise<any>;
  isProcessing: boolean;
}

interface ProgressCallback {
  (progress: { processed: number; total: number; percentage: number }): void;
}

export function useCSVWorker(onProgress?: ProgressCallback): CSVWorkerHookResult {
  const workerRef = useRef<Worker | null>(null);
  const isProcessingRef = useRef(false);

  // Initialize worker
  const getWorker = useCallback(() => {
    if (!workerRef.current) {
      // Create worker from the TypeScript file
      // Note: In production, you'd need to build the worker separately
      const workerCode = `
        // Basic CSV parser implementation for Web Worker
        function parseCSVLine(line, delimiter) {
          const result = [];
          let current = '';
          let inQuotes = false;
          let i = 0;

          while (i < line.length) {
            const char = line[i];
            const nextChar = line[i + 1];

            if (char === '"') {
              if (inQuotes && nextChar === '"') {
                current += '"';
                i += 2;
              } else {
                inQuotes = !inQuotes;
                i++;
              }
            } else if (char === delimiter && !inQuotes) {
              result.push(current.trim());
              current = '';
              i++;
            } else {
              current += char;
              i++;
            }
          }

          result.push(current.trim());
          return result;
        }

        function parseCSV(csvContent, options = {}) {
          const { hasHeader = true, delimiter = ',', skipEmptyLines = true } = options;
          const lines = csvContent.split('\\n');
          const result = [];
          let headers = [];

          for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            if (skipEmptyLines && !line) continue;

            const values = parseCSVLine(line, delimiter);

            if (i === 0 && hasHeader) {
              headers = values;
              continue;
            }

            if (hasHeader) {
              const row = {};
              headers.forEach((header, index) => {
                row[header] = values[index] || '';
              });
              result.push(row);
            } else {
              result.push(values);
            }

            if (i % 100 === 0) {
              self.postMessage({
                type: 'PROGRESS',
                data: { processed: i, total: lines.length, percentage: Math.round((i / lines.length) * 100) }
              });
            }
          }

          return result;
        }

        function validateCSV(csvContent, requiredHeaders = []) {
          const lines = csvContent.split('\\n');
          const errors = [];

          if (lines.length === 0) {
            errors.push('CSV file is empty');
            return { valid: false, errors };
          }

          const headerLine = lines[0];
          const headers = parseCSVLine(headerLine, ',');

          requiredHeaders.forEach(required => {
            if (!headers.includes(required)) {
              errors.push(\`Missing required header: \${required}\`);
            }
          });

          if (lines.length < 2) {
            errors.push('CSV file must contain at least one data row');
          }

          return { valid: errors.length === 0, errors, headers, rowCount: lines.length - 1 };
        }

        self.onmessage = function(e) {
          const { type, data } = e.data;

          try {
            switch (type) {
              case 'PARSE_CSV':
                const parseResult = parseCSV(data.csvContent, data.options);
                self.postMessage({ type: 'PARSE_COMPLETE', data: parseResult });
                break;

              case 'VALIDATE_CSV':
                const validationResult = validateCSV(data.csvContent, ['name', 'epic_number']);
                self.postMessage({ type: 'VALIDATE_COMPLETE', data: validationResult });
                break;

              default:
                throw new Error(\`Unknown message type: \${type}\`);
            }
          } catch (error) {
            self.postMessage({
              type: 'PARSE_ERROR',
              error: error.message || 'Unknown error',
              data: null
            });
          }
        };
      `;

      const blob = new Blob([workerCode], { type: 'application/javascript' });
      workerRef.current = new Worker(URL.createObjectURL(blob));
    }
    return workerRef.current;
  }, []);

  const parseCSV = useCallback(
    (csvContent: string, options?: any): Promise<any[]> => {
      return new Promise((resolve, reject) => {
        const worker = getWorker();
        isProcessingRef.current = true;

        const handleMessage = (e: MessageEvent) => {
          const { type, data, error } = e.data;

          switch (type) {
            case 'PARSE_COMPLETE':
              worker.removeEventListener('message', handleMessage);
              isProcessingRef.current = false;
              resolve(data);
              break;

            case 'PARSE_ERROR':
              worker.removeEventListener('message', handleMessage);
              isProcessingRef.current = false;
              reject(new Error(error));
              break;

            case 'PROGRESS':
              onProgress?.(data);
              break;
          }
        };

        worker.addEventListener('message', handleMessage);
        worker.postMessage({
          type: 'PARSE_CSV',
          data: { csvContent, options },
        });
      });
    },
    [getWorker, onProgress]
  );

  const validateCSV = useCallback(
    (csvContent: string): Promise<any> => {
      return new Promise((resolve, reject) => {
        const worker = getWorker();

        const handleMessage = (e: MessageEvent) => {
          const { type, data, error } = e.data;

          switch (type) {
            case 'VALIDATE_COMPLETE':
              worker.removeEventListener('message', handleMessage);
              resolve(data);
              break;

            case 'VALIDATE_ERROR':
              worker.removeEventListener('message', handleMessage);
              reject(new Error(error));
              break;
          }
        };

        worker.addEventListener('message', handleMessage);
        worker.postMessage({
          type: 'VALIDATE_CSV',
          data: { csvContent },
        });
      });
    },
    [getWorker]
  );

  return {
    parseCSV,
    validateCSV,
    isProcessing: isProcessingRef.current,
  };
}
