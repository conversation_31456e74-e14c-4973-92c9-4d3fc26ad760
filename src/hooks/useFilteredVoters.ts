import { useMemo } from 'react';
import { Voter, Filters } from '../context/AppContext';

/**
 * Custom hook to filter voters based on UI-specific filters only
 * Database-level filtering is handled by the VoterService.getVoters() method
 * This hook only handles UI state filters like selectedPollingStations and selectedSections
 * @param voters - array of voters (already filtered by database)
 * @param filters - current filter state
 * @returns filtered voters array
 */
export function useFilteredVoters(voters: Voter[], filters: Filters): Voter[] {
  return useMemo(() => {
    // If no polling stations are selected, return empty array
    // This ensures that users must select polling stations to see voters
    if (filters.selectedPollingStations.length === 0) {
      return [];
    }

    return voters.filter(voter => {
      // Search term filter (client-side for instant results)
      if (filters.searchTerm && filters.searchTerm.trim()) {
        const searchTerm = filters.searchTerm.toLowerCase().trim();
        const name = (voter.name || '').toLowerCase();
        const epic = (voter.epic || '').toLowerCase();

        if (!name.includes(searchTerm) && !epic.includes(searchTerm)) {
          return false;
        }
      }

      // Gender filter (client-side for instant results)
      if (filters.gender && filters.gender !== 'All') {
        if (voter.gender !== filters.gender) {
          return false;
        }
      }

      // Status filter (client-side for instant results)
      if (filters.status && filters.status !== 'All') {
        const voterStatus = voter.status || 'Active';
        if (voterStatus !== filters.status) {
          return false;
        }
      }

      // Community filter (client-side for instant results)
      if (filters.community && filters.community !== 'All') {
        if (voter.community !== filters.community) {
          return false;
        }
      }

      // Religion filter (client-side for instant results)
      if (filters.religion && filters.religion !== 'All') {
        if (voter.religion !== filters.religion) {
          return false;
        }
      }

      // Economic status filter (client-side for instant results)
      // Support both economicStatus (legacy) and economic_status (new)
      const economicStatusFilter = filters.economic_status || filters.economicStatus;
      if (economicStatusFilter && economicStatusFilter !== 'All') {
        if (voter.economic_status !== economicStatusFilter) {
          return false;
        }
      }

      // Age range filter (client-side for instant results)
      if (filters.ageFrom > 18 || filters.ageTo < 120) {
        const age = voter.age || 0;
        if (age < filters.ageFrom || age > filters.ageTo) {
          return false;
        }
      }

      // Polling station filter (from sidebar selection)
      // Only filter by polling station if specific stations are selected
      if (filters.selectedPollingStations.length > 0) {
        const voterStation = voter.polling_station || voter.pollingStation;
        if (!voterStation || !filters.selectedPollingStations.includes(voterStation)) {
          return false;
        }
      }

      // Section filter (from sidebar selection)
      if (Object.keys(filters.selectedSections).length > 0) {
        const voterStation = voter.polling_station || voter.pollingStation;
        const voterSection = voter.section;

        if (!voterStation || !voterSection) {
          return false;
        }

        const selectedSectionsForStation = filters.selectedSections[voterStation];

        if (!selectedSectionsForStation || !selectedSectionsForStation.includes(voterSection)) {
          return false;
        }
      }

      return true;
    });
  }, [
    voters,
    filters.selectedPollingStations,
    filters.selectedSections,
    filters.searchTerm,
    filters.gender,
    filters.status,
    filters.community,
    filters.religion,
    filters.economic_status,
    filters.economicStatus,
    filters.ageFrom,
    filters.ageTo
  ]);
}

/**
 * Custom hook to calculate voter statistics
 * @param voters - array of voters (usually filtered)
 * @returns statistics object
 */
export function useVoterStats(voters: Voter[]) {
  return useMemo(() => {
    const total = voters.length;
    const male = voters.filter(v => v.gender === 'Male').length;
    const female = voters.filter(v => v.gender === 'Female').length;

    // Calculate households (unique house_number + polling_station combinations)
    const householdKeys = new Set(
      voters
        .filter(v => v.house_number && v.pollingStation) // Only count voters with house numbers
        .map(v => `${v.pollingStation}_${v.house_number}`) // Create unique key
    );
    const households = householdKeys.size;

    // Calculate percentages
    const malePercentage = total > 0 ? ((male / total) * 100).toFixed(1) : '0.0';
    const femalePercentage = total > 0 ? ((female / total) * 100).toFixed(1) : '0.0';
    const avgVotersPerHousehold = households > 0 ? (total / households).toFixed(1) : '0.0';

    return {
      total,
      male,
      female,
      households,
      malePercentage,
      femalePercentage,
      avgVotersPerHousehold,
    };
  }, [voters]);
}
