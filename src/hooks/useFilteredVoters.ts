import { useMemo } from 'react';
import { Voter, Filters } from '../context/AppContext';

/**
 * Custom hook to filter voters based on current filters
 * @param voters - array of voters
 * @param filters - current filter state
 * @returns filtered voters array
 */
export function useFilteredVoters(voters: Voter[], filters: Filters): Voter[] {
  return useMemo(() => {
    // If no polling stations are selected, return empty array
    // This ensures that users must select polling stations to see voters
    if (filters.selectedPollingStations.length === 0) {
      return [];
    }
    return voters.filter(voter => {
      // Gender filter
      if (filters.gender !== 'All' && voter.gender !== filters.gender) {
        return false;
      }

      // Age filter
      if (voter.age !== undefined && (voter.age < filters.ageFrom || voter.age > filters.ageTo)) {
        return false;
      }

      // Community filter
      if (filters.community !== 'All' && voter.community !== filters.community) {
        return false;
      }

      // Religion filter
      if (filters.religion !== 'All' && voter.religion !== filters.religion) {
        return false;
      }

      // Economic status filter
      if (filters.economicStatus !== 'All' && voter.economicStatus !== filters.economicStatus) {
        return false;
      }

      // Status filter
      if (filters.status !== 'All' && voter.status !== filters.status) {
        return false;
      }

      // Supporter status filter
      if (
        filters.supporter_status !== 'All' &&
        voter.supporter_status !== filters.supporter_status
      ) {
        return false;
      }

      // Search term filter (searches name, epic, polling station)
      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        const searchableFields = [
          voter.name,
          voter.epic || voter.epic_number,
          voter.pollingStation || voter.polling_station,
          voter.fatherName || voter.relationship_name || '',
          voter.house_number || '',
        ];

        const matchesSearch = searchableFields.some(field =>
          field?.toLowerCase().includes(searchLower)
        );

        if (!matchesSearch) {
          return false;
        }
      }

      // Polling station filter (from sidebar selection)
      // Only filter by polling station if specific stations are selected
      if (filters.selectedPollingStations.length > 0) {
        const voterStation = voter.polling_station || voter.pollingStation;
        if (!voterStation || !filters.selectedPollingStations.includes(voterStation)) {
          return false;
        }
      }

      // Section filter (from sidebar selection)
      if (Object.keys(filters.selectedSections).length > 0) {
        const voterStation = voter.polling_station || voter.pollingStation;
        const voterSection = voter.section;

        if (!voterStation || !voterSection) {
          return false;
        }

        const selectedSectionsForStation = filters.selectedSections[voterStation];

        if (!selectedSectionsForStation || !selectedSectionsForStation.includes(voterSection)) {
          return false;
        }
      }

      return true;
    });
  }, [voters, filters]);
}

/**
 * Custom hook to calculate voter statistics
 * @param voters - array of voters (usually filtered)
 * @returns statistics object
 */
export function useVoterStats(voters: Voter[]) {
  return useMemo(() => {
    const total = voters.length;
    const male = voters.filter(v => v.gender === 'Male').length;
    const female = voters.filter(v => v.gender === 'Female').length;

    // Calculate households (unique house_number + polling_station combinations)
    const householdKeys = new Set(
      voters
        .filter(v => v.house_number && v.pollingStation) // Only count voters with house numbers
        .map(v => `${v.pollingStation}_${v.house_number}`) // Create unique key
    );
    const households = householdKeys.size;

    // Calculate percentages
    const malePercentage = total > 0 ? ((male / total) * 100).toFixed(1) : '0.0';
    const femalePercentage = total > 0 ? ((female / total) * 100).toFixed(1) : '0.0';
    const avgVotersPerHousehold = households > 0 ? (total / households).toFixed(1) : '0.0';

    return {
      total,
      male,
      female,
      households,
      malePercentage,
      femalePercentage,
      avgVotersPerHousehold,
    };
  }, [voters]);
}
