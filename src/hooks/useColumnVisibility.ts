import { useState, useCallback } from 'react';

export interface ColumnVisibility {
  name: boolean;
  age: boolean;
  gender: boolean;
  epic: boolean;
  pollingStation: boolean;
  affiliation: boolean;
  payee: boolean;
  status: boolean;
}

const defaultVisibility: ColumnVisibility = {
  name: true,
  age: true,
  gender: true,
  epic: true,
  pollingStation: true,
  affiliation: false,
  payee: false,
  status: true,
};

export function useColumnVisibility() {
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility>(defaultVisibility);

  const toggleColumn = useCallback((column: keyof ColumnVisibility) => {
    setVisibleColumns(prev => ({
      ...prev,
      [column]: !prev[column],
    }));
  }, []);

  const getVisibleColumnCount = useCallback(() => {
    return Object.values(visibleColumns).filter(Boolean).length;
  }, [visibleColumns]);

  const resetToDefault = useCallback(() => {
    setVisibleColumns(defaultVisibility);
  }, []);

  return {
    visibleColumns,
    toggleColumn,
    getVisibleColumnCount,
    resetToDefault,
  };
}
