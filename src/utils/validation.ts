/**
 * Shared validation utilities for the application
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Validate transaction data
 */
export function validateTransaction(transaction: {
  date: string;
  purpose: string;
  amount: number;
  voter_id?: number;
}): ValidationResult {
  const errors: string[] = [];

  // Check required fields
  if (transaction.voter_id !== undefined && !transaction.voter_id) {
    errors.push('Voter ID is required');
  }

  if (!transaction.date) {
    errors.push('Date is required');
  }

  if (!transaction.purpose) {
    errors.push('Purpose is required');
  }

  if (!transaction.amount) {
    errors.push('Amount is required');
  }

  // Validate date format (DD-MM-YYYY)
  if (transaction.date) {
    const dateRegex = /^\d{2}-\d{2}-\d{4}$/;
    if (!dateRegex.test(transaction.date)) {
      errors.push('Date must be in DD-MM-YYYY format');
    }
  }

  // Validate amount is positive integer
  if (transaction.amount !== undefined) {
    if (!Number.isInteger(transaction.amount) || transaction.amount <= 0) {
      errors.push('Amount must be a positive integer');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Validate required fields for any object
 */
export function validateRequiredFields(
  data: Record<string, any>,
  requiredFields: string[]
): ValidationResult {
  const errors: string[] = [];

  requiredFields.forEach(field => {
    if (!data[field]) {
      errors.push(`${field} is required`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Validate email format
 */
export function validateEmail(email: string): boolean {
  if (!email) return true; // Optional field
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number format (basic)
 */
export function validatePhone(phone: string): boolean {
  if (!phone) return true; // Optional field
  const phoneRegex = /^[\d\s\-\+\(\)]+$/;
  return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
}

/**
 * Validate EPIC number format
 */
export function validateEpicNumber(epicNumber: string): boolean {
  if (!epicNumber) return false;
  // Basic validation - alphanumeric, minimum 6 characters
  const epicRegex = /^[A-Z0-9]{6,}$/i;
  return epicRegex.test(epicNumber);
}

/**
 * Sanitize string input
 */
export function sanitizeString(input: string): string {
  return input.trim().replace(/\s+/g, ' ');
}

/**
 * Validate year range
 */
export function validateYear(
  year: number,
  minYear = 1900,
  maxYear = new Date().getFullYear()
): boolean {
  return year >= minYear && year <= maxYear;
}
