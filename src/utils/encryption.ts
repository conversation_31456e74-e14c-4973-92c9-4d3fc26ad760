import CryptoJS from 'crypto-js';

// Simple encryption key - in production, this should be more secure
const ENCRYPTION_KEY = 'electixir-transaction-key-2024';

/**
 * Encryption service for transaction amounts
 * Uses AES encryption to protect sensitive financial data
 */
export class EncryptionService {
  /**
   * Encrypt a transaction amount
   * @param amount - The amount to encrypt (number)
   * @returns Encrypted string
   */
  static encryptAmount(amount: number): string {
    try {
      const encrypted = CryptoJS.AES.encrypt(amount.toString(), ENCRYPTION_KEY).toString();
      return encrypted;
    } catch (error) {
      console.error('Failed to encrypt amount:', error);
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypt a transaction amount
   * @param encryptedAmount - The encrypted amount string
   * @returns Decrypted amount as number
   */
  static decryptAmount(encryptedAmount: string): number {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedAmount, ENCRYPTION_KEY);
      const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);
      
      if (!decryptedString) {
        console.error('Failed to decrypt amount: empty result');
        return 0;
      }
      
      const amount = parseInt(decryptedString, 10);
      if (isNaN(amount)) {
        console.error('Failed to decrypt amount: invalid number');
        return 0;
      }
      
      return amount;
    } catch (error) {
      console.error('Failed to decrypt amount:', error);
      return 0;
    }
  }

  /**
   * Test encryption/decryption functionality
   * @param testAmount - Amount to test with
   * @returns True if test passes
   */
  static testEncryption(testAmount: number = 12345): boolean {
    try {
      const encrypted = this.encryptAmount(testAmount);
      const decrypted = this.decryptAmount(encrypted);
      return decrypted === testAmount;
    } catch (error) {
      console.error('Encryption test failed:', error);
      return false;
    }
  }
}

// Test encryption on module load in development
if (process.env.NODE_ENV === 'development') {
  const testPassed = EncryptionService.testEncryption();
  if (testPassed) {
    console.log('✅ Encryption service initialized successfully');
  } else {
    console.error('❌ Encryption service test failed');
  }
}
