/**
 * Diagnostic utilities to help identify database and functionality issues
 */

import DatabaseService from '../database/DatabaseService';
import VoterService from '../database/VoterService';
import TransactionService from '../database/TransactionService';
import { EncryptionService } from './encryption';

export class DiagnosticService {
  /**
   * Run comprehensive diagnostics on the database and services
   */
  static async runDiagnostics(): Promise<{
    success: boolean;
    results: { [key: string]: { success: boolean; message: string; error?: any } };
  }> {
    const results: { [key: string]: { success: boolean; message: string; error?: any } } = {};

    // Test database initialization
    try {
      const dbService = DatabaseService.getInstance();
      dbService.getDatabase();
      results.database = {
        success: true,
        message: 'Database is initialized and accessible'
      };
    } catch (error) {
      results.database = {
        success: false,
        message: 'Database initialization failed',
        error
      };
    }

    // Test encryption service
    try {
      const testPassed = EncryptionService.testEncryption(12345);
      results.encryption = {
        success: testPassed,
        message: testPassed ? 'Encryption service working correctly' : 'Encryption test failed'
      };
    } catch (error) {
      results.encryption = {
        success: false,
        message: 'Encryption service error',
        error
      };
    }

    // Test voter service
    try {
      const voterService = new VoterService();
      const voters = await voterService.getVoters();
      results.voterService = {
        success: true,
        message: `Voter service working - ${voters.length} voters loaded`
      };
    } catch (error) {
      results.voterService = {
        success: false,
        message: 'Voter service failed',
        error
      };
    }

    // Test transaction service
    try {
      const transactionService = new TransactionService();
      const transactions = await transactionService.getAllTransactions();
      results.transactionService = {
        success: true,
        message: `Transaction service working - ${transactions.length} transactions loaded`
      };
    } catch (error) {
      results.transactionService = {
        success: false,
        message: 'Transaction service failed',
        error
      };
    }

    // Test polling stations
    try {
      const voterService = new VoterService();
      const stations = await voterService.getPollingStations();
      results.pollingStations = {
        success: true,
        message: `Polling stations loaded - ${stations.length} stations found`
      };
    } catch (error) {
      results.pollingStations = {
        success: false,
        message: 'Failed to load polling stations',
        error
      };
    }

    const overallSuccess = Object.values(results).every(result => result.success);

    return {
      success: overallSuccess,
      results
    };
  }

  /**
   * Log diagnostic results to console
   */
  static async logDiagnostics(): Promise<void> {
    console.log('🔍 Running Electixir Diagnostics...');

    const diagnostics = await this.runDiagnostics();

    console.log(`\n📊 Overall Status: ${diagnostics.success ? '✅ PASS' : '❌ FAIL'}\n`);

    Object.entries(diagnostics.results).forEach(([test, result]) => {
      const icon = result.success ? '✅' : '❌';
      console.log(`${icon} ${test}: ${result.message}`);
      if (result.error) {
        console.error(`   Error:`, result.error);
      }
    });

    console.log('\n🔍 Diagnostics Complete\n');
  }
}