/**
 * Shared database utilities and query helpers
 */

import { Database } from 'sql.js';

/**
 * Execute a prepared statement and return results
 */
export function executeQuery<T = any>(db: Database, query: string, params: any[] = []): T[] {
  const stmt = db.prepare(query);

  try {
    if (params.length > 0) {
      stmt.bind(params);
    }

    const results: T[] = [];
    while (stmt.step()) {
      results.push(stmt.getAsObject() as T);
    }

    return results;
  } finally {
    stmt.free();
  }
}

/**
 * Execute a query and return the first result
 */
export function executeQuerySingle<T = any>(
  db: Database,
  query: string,
  params: any[] = []
): T | null {
  const results = executeQuery<T>(db, query, params);
  return results.length > 0 ? results[0] : null;
}

/**
 * Execute an insert/update/delete query and return affected rows
 */
export function executeUpdate(db: Database, query: string, params: any[] = []): number {
  const stmt = db.prepare(query);

  try {
    if (params.length > 0) {
      stmt.bind(params);
    }

    stmt.run();
    return db.getRowsModified();
  } finally {
    stmt.free();
  }
}

/**
 * Execute an insert query and return the last inserted ID
 */
export function executeInsert(db: Database, query: string, params: any[] = []): number {
  const stmt = db.prepare(query);

  try {
    if (params.length > 0) {
      stmt.bind(params);
    }

    stmt.run();

    // Get the inserted ID
    const result = db.exec('SELECT last_insert_rowid() as id');
    return result[0].values[0][0] as number;
  } finally {
    stmt.free();
  }
}

/**
 * Check if a record exists
 */
export function recordExists(
  db: Database,
  table: string,
  whereClause: string,
  params: any[] = []
): boolean {
  const query = `SELECT 1 FROM ${table} WHERE ${whereClause} LIMIT 1`;
  const result = executeQuerySingle(db, query, params);
  return result !== null;
}

/**
 * Get count of records matching criteria
 */
export function getRecordCount(
  db: Database,
  table: string,
  whereClause: string = '1=1',
  params: any[] = []
): number {
  const query = `SELECT COUNT(*) as count FROM ${table} WHERE ${whereClause}`;
  const result = executeQuerySingle<{ count: number }>(db, query, params);
  return result?.count || 0;
}

/**
 * Build WHERE clause from filters
 */
export function buildWhereClause(filters: Record<string, any>): {
  whereClause: string;
  params: any[];
} {
  const conditions: string[] = [];
  const params: any[] = [];

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (typeof value === 'string' && value.includes('%')) {
        conditions.push(`${key} LIKE ?`);
      } else {
        conditions.push(`${key} = ?`);
      }
      params.push(value);
    }
  });

  return {
    whereClause: conditions.length > 0 ? conditions.join(' AND ') : '1=1',
    params,
  };
}

/**
 * Sanitize table/column names to prevent SQL injection
 */
export function sanitizeIdentifier(identifier: string): string {
  // Only allow alphanumeric characters and underscores
  return identifier.replace(/[^a-zA-Z0-9_]/g, '');
}

/**
 * Build ORDER BY clause
 */
export function buildOrderByClause(orderBy?: string, direction: 'ASC' | 'DESC' = 'ASC'): string {
  if (!orderBy) return '';

  const sanitizedColumn = sanitizeIdentifier(orderBy);
  return `ORDER BY ${sanitizedColumn} ${direction}`;
}

/**
 * Build LIMIT clause for pagination
 */
export function buildLimitClause(page?: number, pageSize?: number): string {
  if (!page || !pageSize) return '';

  const offset = (page - 1) * pageSize;
  return `LIMIT ${pageSize} OFFSET ${offset}`;
}
