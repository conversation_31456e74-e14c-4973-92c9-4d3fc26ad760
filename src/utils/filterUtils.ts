import VoterService from '../database/VoterService';
import SettingsService from '../database/SettingsService';

/**
 * Utility functions for handling filter options across the application
 */
export class FilterUtils {
  private static voterService = new VoterService();
  private static settingsService = new SettingsService();

  /**
   * Get polling stations sorted numerically
   */
  static async getPollingStations(): Promise<string[]> {
    try {
      const pollingStations = await this.voterService.getPollingStations();
      // Sort numerically, not alphabetically
      return pollingStations.sort((a, b) =>
        a.localeCompare(b, undefined, { numeric: true, sensitivity: 'base' })
      );
    } catch (error) {
      console.error('Failed to get polling stations:', error);
      return [];
    }
  }

  /**
   * Get sections for a specific polling station
   */
  static async getSectionsForPollingStation(pollingStationName: string): Promise<string[]> {
    try {
      if (!pollingStationName) return [];
      return await this.voterService.getSectionsForPollingStation(pollingStationName);
    } catch (error) {
      console.error('Failed to get sections for polling station:', error);
      return [];
    }
  }

  /**
   * Get voter status options
   */
  static getVoterStatusOptions(): string[] {
    return ['Active', 'Expired', 'Shifted', 'Duplicate', 'Missing', 'Disqualified'];
  }

  /**
   * Get supporter status options
   */
  static getSupporterStatusOptions(): string[] {
    return ['Strong Supporter', 'Potential Supporter', 'Undecided', 'Opposed'];
  }

  /**
   * Get community options from settings
   */
  static async getCommunityOptions(): Promise<string[]> {
    try {
      return await this.settingsService.getCategorySettings('community');
    } catch (error) {
      console.error('Failed to get community options:', error);
      return [];
    }
  }

  /**
   * Get religion options from settings
   */
  static async getReligionOptions(): Promise<string[]> {
    try {
      return await this.settingsService.getCategorySettings('religion');
    } catch (error) {
      console.error('Failed to get religion options:', error);
      return [];
    }
  }

  /**
   * Get economic status options from settings
   */
  static async getEconomicStatusOptions(): Promise<string[]> {
    try {
      return await this.settingsService.getCategorySettings('economic_status');
    } catch (error) {
      console.error('Failed to get economic status options:', error);
      return [];
    }
  }

  /**
   * Get education options from settings
   */
  static async getEducationOptions(): Promise<string[]> {
    try {
      return await this.settingsService.getCategorySettings('education');
    } catch (error) {
      console.error('Failed to get education options:', error);
      return [];
    }
  }

  /**
   * Get occupation options from settings
   */
  static async getOccupationOptions(): Promise<string[]> {
    try {
      return await this.settingsService.getCategorySettings('occupation');
    } catch (error) {
      console.error('Failed to get occupation options:', error);
      return [];
    }
  }

  /**
   * Get transaction purpose options from settings
   */
  static async getTransactionPurposeOptions(): Promise<string[]> {
    try {
      return await this.settingsService.getCategorySettings('transaction_purpose');
    } catch (error) {
      console.error('Failed to get transaction purpose options:', error);
      return [];
    }
  }

  /**
   * Get all category options for voter detail panel
   */
  static async getVoterDetailCategoryOptions() {
    try {
      const [community, religion, economic_status, education, occupation, transaction_purpose] = await Promise.all([
        this.getCommunityOptions(),
        this.getReligionOptions(),
        this.getEconomicStatusOptions(),
        this.getEducationOptions(),
        this.getOccupationOptions(),
        this.getTransactionPurposeOptions(),
      ]);

      return {
        community,
        religion,
        economic_status,
        education,
        occupation,
        transaction_purpose,
      };
    } catch (error) {
      console.error('Failed to get voter detail category options:', error);
      return {
        community: [],
        religion: [],
        economic_status: [],
        education: [],
        occupation: [],
        transaction_purpose: [],
      };
    }
  }

  /**
   * Get all filter options for reports
   */
  static async getReportFilterOptions() {
    try {
      const [pollingStations, communities, religions, economicStatuses] = await Promise.all([
        this.getPollingStations(),
        this.getCommunityOptions(),
        this.getReligionOptions(),
        this.getEconomicStatusOptions(),
      ]);

      return {
        pollingStations,
        communities,
        religions,
        economicStatuses,
        voterStatuses: this.getVoterStatusOptions(),
        supporterStatuses: this.getSupporterStatusOptions(),
      };
    } catch (error) {
      console.error('Failed to get report filter options:', error);
      return {
        pollingStations: [],
        communities: [],
        religions: [],
        economicStatuses: [],
        voterStatuses: this.getVoterStatusOptions(),
        supporterStatuses: this.getSupporterStatusOptions(),
      };
    }
  }
}