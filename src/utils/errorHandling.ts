/**
 * Shared error handling utilities for the application
 */

export interface ErrorResult {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * Standardized error handler for database operations
 */
export function handleDatabaseError(error: unknown, operation: string): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`❌ ${operation} failed:`, error);
  throw new Error(`${operation} failed: ${errorMessage}`);
}

/**
 * Safe async operation wrapper with error handling
 */
export async function safeAsyncOperation<T>(
  operation: () => Promise<T>,
  operationName: string
): Promise<ErrorResult> {
  try {
    const data = await operation();
    return { success: true, data };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ ${operationName} failed:`, error);
    return { success: false, error: errorMessage };
  }
}

/**
 * Standardized error logging
 */
export function logError(context: string, error: unknown, additionalInfo?: any): void {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`❌ [${context}] ${errorMessage}`, {
    error,
    additionalInfo,
    timestamp: new Date().toISOString(),
  });
}

/**
 * Create user-friendly error messages
 */
export function createUserFriendlyError(error: unknown, fallbackMessage: string): string {
  if (error instanceof Error) {
    // Map common technical errors to user-friendly messages
    if (error.message.includes('UNIQUE constraint failed')) {
      return 'This record already exists. Please check for duplicates.';
    }
    if (error.message.includes('NOT NULL constraint failed')) {
      return 'Required information is missing. Please fill in all required fields.';
    }
    if (error.message.includes('FOREIGN KEY constraint failed')) {
      return 'This operation cannot be completed due to related data dependencies.';
    }
    if (error.message.includes('localStorage')) {
      return 'Storage limit reached. Please clear some data or export your database.';
    }
    if (error.message.includes('network') || error.message.includes('fetch')) {
      return 'Network connection issue. Please check your internet connection.';
    }

    // Return the original message if it's already user-friendly
    return error.message;
  }

  return fallbackMessage;
}

/**
 * Retry mechanism for operations that might fail temporarily
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: unknown;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      if (attempt === maxRetries) {
        break;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError;
}

/**
 * Validate and throw error if validation fails
 */
export function validateOrThrow(condition: boolean, message: string): void {
  if (!condition) {
    throw new Error(message);
  }
}
