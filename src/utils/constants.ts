/**
 * Application constants and enums
 */

// Voter status options
export const VOTER_STATUS = {
  ACTIVE: 'Active',
  EXPIRED: 'Expired',
  SHIFTED: 'Shifted',
  DUPLICATE: 'Duplicate',
  MISSING: 'Missing',
  DISQUALIFIED: 'Disqualified',
} as const;

export type VoterStatus = (typeof VOTER_STATUS)[keyof typeof VOTER_STATUS];

// Supporter status options
export const SUPPORTER_STATUS = {
  STRONG_SUPPORTER: 'Strong Supporter',
  POTENTIAL_SUPPORTER: 'Potential Supporter',
  UNDECIDED: 'Undecided',
  OPPOSED: 'Opposed',
} as const;

export type SupporterStatus = (typeof SUPPORTER_STATUS)[keyof typeof SUPPORTER_STATUS];

// Gender options
export const GENDER = {
  MALE: 'Male',
  FEMALE: 'Female',
  OTHER: 'Other',
} as const;

export type Gender = (typeof GENDER)[keyof typeof GENDER];

// Relationship types
export const RELATIONSHIP_TYPE = {
  FATHER: 'Father',
  MOTHER: 'Mother',
  HUSBAND: 'Husband',
  OTHERS: 'Others',
} as const;

export type RelationshipType = (typeof RELATIONSHIP_TYPE)[keyof typeof RELATIONSHIP_TYPE];

// Pagination constants
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 9,
  MAX_PAGE_SIZE: 100,
  MIN_PAGE_SIZE: 5,
} as const;

// Validation constants
export const VALIDATION = {
  MIN_AGE: 18,
  MAX_AGE: 120,
  MIN_BIRTH_YEAR: 1900,
  MAX_BIRTH_YEAR: new Date().getFullYear(),
  MIN_EPIC_LENGTH: 6,
  MAX_EPIC_LENGTH: 20,
  MIN_PHONE_DIGITS: 10,
  MAX_PHONE_DIGITS: 15,
} as const;

// Date formats
export const DATE_FORMATS = {
  DISPLAY: 'DD-MM-YYYY',
  ISO: 'YYYY-MM-DD',
  TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
} as const;

// File size limits
export const FILE_LIMITS = {
  MAX_CSV_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_DATABASE_SIZE: 50 * 1024 * 1024, // 50MB
} as const;

// CSV headers mapping
export const CSV_HEADERS = {
  REQUIRED: ['name', 'epic_number'],
  OPTIONAL: [
    'relation_type',
    'relation_name',
    'house_number',
    'birth_year',
    'gender',
    'polling_station',
    'section',
    'phone',
    'email',
    'facebook',
    'instagram',
    'twitter',
    'status',
    'supporter_status',
    'education',
    'occupation',
    'community',
    'religion',
    'economic_status',
    'custom_notes',
  ],
} as const;

// Export formats
export const EXPORT_FORMATS = {
  CSV: 'csv',
  PDF: 'pdf',
  SQLITE: 'sqlite',
} as const;

// Theme options
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
} as const;

export type Theme = (typeof THEMES)[keyof typeof THEMES];

// Local storage keys
export const STORAGE_KEYS = {
  DATABASE: 'electixir_database',
  THEME: 'electixir_theme',
  COLUMN_VISIBILITY: 'electixir_column_visibility',
  FILTERS: 'electixir_filters',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  DATABASE_NOT_INITIALIZED: 'Database not initialized. Please refresh the page.',
  INVALID_FILE_FORMAT: 'Invalid file format. Please select a valid file.',
  FILE_TOO_LARGE: 'File is too large. Please select a smaller file.',
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  VALIDATION_ERROR: 'Please correct the validation errors and try again.',
  DUPLICATE_RECORD: 'A record with this information already exists.',
  REQUIRED_FIELDS_MISSING: 'Please fill in all required fields.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  VOTER_ADDED: 'Voter added successfully',
  VOTER_UPDATED: 'Voter updated successfully',
  VOTER_DELETED: 'Voter deleted successfully',
  CSV_IMPORTED: 'CSV file imported successfully',
  DATABASE_EXPORTED: 'Database exported successfully',
  DATABASE_IMPORTED: 'Database imported successfully',
  DATABASE_CLEARED: 'Database cleared successfully',
} as const;

// Animation durations (in milliseconds)
export const ANIMATION_DURATION = {
  FAST: 150,
  MEDIUM: 250,
  SLOW: 350,
  LIGHT_SWEEP: 1500,
} as const;


