/* ===== REPORTS STYLES ===== */

/* Reports View Container - Full Screen */
#reports-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  padding: var(--space-md) var(--space-md) var(--space-md) var(--space-2xl);
  background: var(--bg-primary);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

#reports-view .toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg) var(--space-2xl);
  background: var(--bg-overlay);
  border-bottom: 1px solid var(--border-primary);
  backdrop-filter: var(--blur-light);
}

#reports-view .toolbar h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

#reports-view .toolbar-left,
#reports-view .toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

/* Reports Content */
.reports-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Report Tabs */
.report-tabs {
  display: flex;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
}

.report-tab {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl);
  background: transparent;
  border: none;
  color: var(--text-tertiary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  border-bottom: 2px solid transparent;
  position: relative;
}

.report-tab:hover {
  color: var(--text-secondary);
  background: var(--bg-button-hover);
}

.report-tab.active {
  color: var(--text-selection);
  border-bottom-color: var(--color-primary);
  background: var(--bg-selection);
}

.report-tab svg {
  opacity: 0.7;
}

.report-tab.active svg {
  opacity: 1;
}

/* Report Container */
.report-container {
  flex: 1;
  overflow-y: auto;
  padding-block: var(--space-2xl);
}

.report-container {
  padding-right: 16px;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: var(--text-tertiary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-primary);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-lg);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--space-xs);
}

/* Export button disabled state */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Report Header */
.report-header {
  margin-bottom: var(--space-2xl);
}

.report-header h2 {
  margin-inline: 20px;
  margin-bottom: var(--space-sm);
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.report-header p {
  display: none;
  margin: 0;
  color: var(--text-tertiary);
  font-size: 14px;
  line-height: 1.5;
}

/* Report Filters */
.report-filters {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-2xl);
  backdrop-filter: var(--blur-light);
}

.report-filters .filter-header {
  border: 0;
  margin: 0;
  min-height: 52px;
}

.filter-toggle-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color var(--transition-fast);
  position: relative;
}

.filter-toggle-btn:hover {
  color: var(--text-primary);
}

.filter-toggle-btn.expanded svg {
  transform: rotate(180deg);
}

.filter-content {
  border-top: 1px solid var(--border-primary);
}

.filter-count {
  background: var(--color-primary);
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: var(--space-xs);
}

.clear-filters-btn {
  background: transparent;
  border: 1px solid var(--border-secondary);
  color: var(--text-tertiary);
  font-size: 12px;
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.clear-filters-btn:hover {
  background: var(--bg-button-hover);
  color: var(--text-secondary);
}

.filter-content {
  padding: var(--space-xl);
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
}

.filter-group label {
  font-size: 12px;
  color: var(--text-tertiary);
  font-weight: 500;
}

.filter-group input,
.filter-group select {
  background: var(--bg-input);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: 8px 12px;
  color: var(--text-secondary);
  font-size: 13px;
  backdrop-filter: var(--blur-light);
  transition:
    border-color var(--transition-medium),
    box-shadow var(--transition-medium);
  -webkit-app-region: var(--app-region-no-drag);
  width: 100%;
  min-width: 120px;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: var(--border-active);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.filter-group select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--bg-secondary);
}

.select-wrapper {
  position: relative;
}

.select-wrapper::after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid var(--text-tertiary);
  pointer-events: none;
}

.select-wrapper select {
  -webkit-appearance: none;
  appearance: none;
  padding-right: 30px;
}

/* Report Summary */
.report-summary {
  margin-bottom: var(--space-2xl);
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.summary-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 20px;
  position: relative;
  backdrop-filter: var(--blur-medium);
  transition:
    transform var(--transition-medium),
    box-shadow var(--transition-medium);
  cursor: default;
  transform: translateZ(0);
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-primary);
}

.summary-card .stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.summary-card .stat-label {
  color: var(--text-tertiary);
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-card .stat-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  backdrop-filter: var(--blur-light);
}

.stat-icon svg {
  width: 16px;
  height: 16px;
}

.summary-card .stat-icon.blue {
  background: var(--bg-icon-blue);
  color: var(--text-icon-blue);
}

.summary-card .stat-icon.green {
  background: var(--bg-icon-green);
  color: var(--text-icon-green);
}

.summary-card .stat-icon.orange {
  background: var(--bg-icon-orange);
  color: var(--text-icon-orange);
}

.summary-card .stat-icon.red {
  background: var(--bg-icon-red);
  color: var(--text-icon-red);
}

.summary-card .stat-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
}

.summary-card .stat-change {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.summary-card .stat-change.positive {
  color: var(--text-positive);
}

.summary-card .stat-change.negative {
  color: var(--text-negative);
}

/* Charts Grid */
.charts-grid {
  margin-bottom: var(--space-2xl);
}

.chart-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-2xl);
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-2xl);
}

.chart-row .chart-item.full-width {
  grid-column: 1 / -1;
}

.chart-item {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  backdrop-filter: var(--blur-light);
}

.chart-container {
  position: relative;
  width: 100%;
}

/* Doughnut Center Text */
.doughnut-center-text {
  pointer-events: none;
  user-select: none;
}

/* Tables */
.station-details,
.household-details,
.beneficiary-details {
  margin-bottom: var(--space-2xl);
}

.station-details h3,
.household-details h3,
.beneficiary-details h3 {
  margin: 0 0 var(--space-xl) 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.station-table,
.household-table,
.beneficiary-table {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  backdrop-filter: var(--blur-medium);
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
}

.station-table table,
.household-table table,
.beneficiary-table table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  transform: translateZ(0);
}

.station-table th,
.household-table th,
.beneficiary-table th {
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  font-weight: 500;
  font-size: 12px;
  text-align: left;
  padding: 12px 0 12px 20px;
  border-bottom: 1px solid var(--border-primary);
  backdrop-filter: var(--blur-light);
  user-select: none;
  position: relative;
}

.station-table td,
.household-table td,
.beneficiary-table td {
  padding: 8px 0 8px 20px;
  height: 50px;
  border-bottom: 1px solid var(--border-primary);
  color: var(--text-secondary);
  font-size: 13px;
  cursor: default;
  position: relative;
}

.station-table th:last-child,
.household-table th:last-child,
.beneficiary-table th:last-child,
.station-table td:last-child,
.household-table td:last-child,
.beneficiary-table td:last-child {
  padding: 8px 20px;
}

.station-table tr,
.household-table tr,
.beneficiary-table tr {
  transition: background-color var(--transition-fast);
}

.station-table tr:last-child td,
.household-table tr:last-child td,
.beneficiary-table tr:last-child td {
  border-bottom: none;
}

.station-table tr:hover,
.household-table tr:hover,
.beneficiary-table tr:hover {
  background: var(--bg-row-hover);
}

.station-table tr.selected,
.household-table tr.selected,
.beneficiary-table tr.selected {
  background: var(--bg-selection);
}

.station-name,
.household-name,
.beneficiary-name {
  font-weight: 500;
  color: var(--text-primary);
}

.amount {
  font-weight: 500;
  color: var(--text-positive);
}

.support-rate {
  font-weight: 500;
}

.supporter-status {
  padding: 3px 6px;
  border-radius: var(--radius-sm);
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  backdrop-filter: var(--blur-light);
}

.supporter-status.strong-supporter {
  background: var(--bg-icon-green);
  color: var(--text-icon-green);
}

.supporter-status.potential-supporter {
  background: var(--bg-icon-orange);
  color: var(--text-icon-orange);
}

.supporter-status.undecided {
  background: var(--bg-icon-blue);
  color: var(--text-icon-blue);
}

.supporter-status.opposed {
  background: var(--bg-icon-red);
  color: var(--text-icon-red);
}

/* Insights Section */
.insights-section {
  margin-bottom: var(--space-2xl);
}

.insights-section h3 {
  margin: 0 0 var(--space-lg) 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
}

.insight-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  text-align: center;
  backdrop-filter: var(--blur-light);
  transition: all var(--transition-fast);
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-primary);
}

.insight-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--space-sm);
}

.insight-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-primary);
  line-height: 1;
  margin-bottom: var(--space-xs);
}

.insight-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .chart-row {
    grid-template-columns: 1fr;
  }

  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 900px) {
  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .station-table,
  .household-table,
  .beneficiary-table {
    font-size: 12px;
  }

  .station-table th,
  .household-table th,
  .beneficiary-table th,
  .station-table td,
  .household-table td,
  .beneficiary-table td {
    padding: 8px 12px;
    font-size: 11px;
  }

  .station-table th:last-child,
  .household-table th:last-child,
  .beneficiary-table th:last-child,
  .station-table td:last-child,
  .household-table td:last-child,
  .beneficiary-table td:last-child {
    padding: 8px 12px;
  }

  .station-name,
  .household-name,
  .beneficiary-name {
    font-size: 12px;
  }

  .supporter-status {
    font-size: 10px;
    padding: 2px 4px;
  }
}

@media (max-width: 768px) {
  .report-container {
    padding: var(--space-lg);
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .report-tabs {
    overflow-x: auto;
  }

  .report-tab {
    white-space: nowrap;
    padding: var(--space-md) var(--space-lg);
  }

  .summary-cards {
    grid-template-columns: 1fr;
  }

  .summary-card .stat-value {
    font-size: 24px;
  }

  .insight-value {
    font-size: 20px;
  }

  /* Table responsive adjustments */
  .station-table,
  .household-table,
  .beneficiary-table {
    overflow-x: auto;
  }

  .station-table th,
  .household-table th,
  .beneficiary-table th,
  .station-table td,
  .household-table td,
  .beneficiary-table td {
    padding: 12px 16px;
    font-size: 12px;
  }

  .station-table th:last-child,
  .household-table th:last-child,
  .beneficiary-table th:last-child,
  .station-table td:last-child,
  .household-table td:last-child,
  .beneficiary-table td:last-child {
    padding: 12px 16px;
  }
}
