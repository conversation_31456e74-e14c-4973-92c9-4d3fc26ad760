.virtualized-table {
  border: 1px solid var(--color-border);
  border-radius: 8px;
  overflow: hidden;
  background: var(--color-background);
}

.table-header {
  background: var(--color-background-secondary);
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-row {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--color-border-light);
}

.table-row:last-child {
  border-bottom: none;
}

.header-row {
  font-weight: 600;
  background: var(--color-background-secondary);
}

.data-row {
  background: var(--color-background);
  transition: background-color 0.15s ease;
}

.data-row:hover {
  background: var(--color-background-hover);
  cursor: pointer;
}

.data-row:nth-child(even) {
  background: var(--color-background-alt);
}

.data-row:nth-child(even):hover {
  background: var(--color-background-hover);
}

.table-cell {
  flex: 1;
  padding: 12px 16px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-cell {
  color: var(--color-text-secondary);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.data-cell {
  color: var(--color-text);
  font-size: 14px;
}

.table-body {
  position: relative;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .table-cell {
    padding: 8px 12px;
    font-size: 13px;
  }

  .header-cell {
    font-size: 12px;
  }
}

/* Loading state */
.virtualized-table.loading {
  opacity: 0.6;
  pointer-events: none;
}

.virtualized-table.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
