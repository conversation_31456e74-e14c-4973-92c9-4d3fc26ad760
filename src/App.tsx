import './normalize.css';
import './styles.css';
import './styles/Reports.css';
import Sidebar from './components/Sidebar';
import VoterDetailPanel from './components/VoterDetailPanel';
import MainContent from './components/MainContent';
import ReportsView from './components/ReportsView';
import LoginScreen from './components/LoginScreen';
import { ThemeProvider } from './context/ThemeContext';
import { AppProvider } from './context/AppContext';
import { AuthProvider, useAuth } from './context/AuthContext';
import { DialogProvider } from './components/DialogProvider';
import { useApp } from './context/AppContext';

function AppContent() {
  const { state, dispatch } = useApp();
  const { state: authState } = useAuth();

  const handleBackToMain = () => {
    dispatch({ type: 'SET_CURRENT_VIEW', payload: 'main' });
  };

  // Show loading state while checking authentication
  if (authState.isLoading) {
    return (
      <div className="loading-screen">
        <div className="loading-spinner">
          <svg
            className="spinner"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M21 12a9 9 0 11-6.219-8.56" />
          </svg>
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  // Show login screen if not authenticated
  if (!authState.isAuthenticated) {
    return <LoginScreen />;
  }

  // If we're in reports view, show full-screen reports
  if (state.currentView === 'reports') {
    return <ReportsView onBackToMain={handleBackToMain} />;
  }

  // Otherwise show the normal layout with sidebar (no header)
  return (
    <>
      <Sidebar />
      <MainContent />
      <VoterDetailPanel />
    </>
  );
}

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <AppProvider>
          <DialogProvider>
            <AppContent />
          </DialogProvider>
        </AppProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
