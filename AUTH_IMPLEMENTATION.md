# Enhanced Authentication System - Implementation Guide

## Overview
Enhanced offline authentication for personal use with 2-5 users max. Uses bcrypt for password hashing and AES encryption for transaction data.

## Dependencies

First, install required packages:

```bash
npm install bcryptjs crypto-js
npm install --save-dev @types/bcryptjs @types/crypto-js
```

## Database Changes

### 1. Add Users Table
Add to `src/database/DatabaseService.ts` in the `createTables()` method:

```sql
-- Users table (add to existing createTablesSQL)
CREATE TABLE IF NOT EXISTS users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT NOT NULL UNIQUE,
  password_hash TEXT NOT NULL,
  role TEXT CHECK(role IN ('admin', 'user')) DEFAULT 'user',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Note: Default admin user will be created programmatically with bcrypt hash
```

### 2. Update Transaction Table
Modify the existing transaction table to support encryption:

```sql
-- Update transactions table (modify existing in createTablesSQL)
CREATE TABLE IF NOT EXISTS transactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  voter_id INTEGER NOT NULL,
  date DATE NOT NULL,
  purpose TEXT NOT NULL,
  amount_encrypted TEXT NOT NULL,  -- Changed from amount INTEGER
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (voter_id) REFERENCES voters(id)
);
```

## Implementation Steps

### Phase 1: Core Authentication (2-3 hours)

#### 1.1 Create Auth Context (`src/context/AuthContext.tsx`)
```tsx
import { createContext, useContext, useReducer, ReactNode } from 'react';

interface User {
  id: number;
  username: string;
  role: 'admin' | 'user';
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface AuthContextType {
  state: AuthState;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isAdmin: () => boolean;
  canAccessTransactions: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: ReactNode }) {
  // Implementation here
  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}
```

#### 1.2 Create Login Component (`src/components/LoginScreen.tsx`)
```tsx
import { useState } from 'react';
import { useAuth } from '../context/AuthContext';

export default function LoginScreen() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await login(username, password);
    if (!success) {
      setError('Invalid credentials');
    }
  };

  return (
    <div className="login-screen">
      <form onSubmit={handleSubmit}>
        <h1>Login</h1>
        {error && <div className="error">{error}</div>}
        <input
          type="text"
          placeholder="Username"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          required
        />
        <input
          type="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
        <button type="submit">Login</button>
      </form>
    </div>
  );
}
```

#### 1.3 Create Auth Service (`src/services/AuthService.ts`)
```ts
import bcrypt from 'bcryptjs';
import { DatabaseService } from '../database/DatabaseService';

export interface User {
  id: number;
  username: string;
  role: 'admin' | 'user';
}

export class AuthService {
  private dbService: DatabaseService;

  constructor() {
    this.dbService = DatabaseService.getInstance();
  }

  async validateUser(username: string, password: string): Promise<User | null> {
    const db = this.dbService.getDatabase();
    if (!db) return null;

    const stmt = db.prepare('SELECT * FROM users WHERE username = ?');
    stmt.bind([username]);

    if (stmt.step()) {
      const row = stmt.getAsObject();
      stmt.free();

      // Verify password with bcrypt
      const isValid = await bcrypt.compare(password, row.password_hash as string);
      if (isValid) {
        return {
          id: row.id as number,
          username: row.username as string,
          role: row.role as 'admin' | 'user'
        };
      }
    } else {
      stmt.free();
    }

    return null;
  }

  async createUser(username: string, password: string, role: 'admin' | 'user' = 'user'): Promise<boolean> {
    const db = this.dbService.getDatabase();
    if (!db) return false;

    try {
      // Hash password with bcrypt
      const saltRounds = 10;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      const stmt = db.prepare('INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)');
      stmt.bind([username, passwordHash, role]);
      stmt.step();
      stmt.free();

      // Save to localStorage
      this.dbService.saveToLocalStorage();
      return true;
    } catch (error) {
      console.error('Failed to create user:', error);
      return false;
    }
  }

  async ensureDefaultAdmin(): Promise<void> {
    const db = this.dbService.getDatabase();
    if (!db) return;

    // Check if admin exists
    const stmt = db.prepare('SELECT COUNT(*) as count FROM users WHERE username = ?');
    stmt.bind(['admin']);

    if (stmt.step()) {
      const row = stmt.getAsObject();
      const count = row.count as number;
      stmt.free();

      if (count === 0) {
        // Create default admin with bcrypt hash
        await this.createUser('admin', 'mawhati8123', 'admin');
        console.log('✅ Default admin user created');
      }
    } else {
      stmt.free();
    }
  }

  saveSession(user: User): void {
    localStorage.setItem('auth_user', JSON.stringify(user));
  }

  getSession(): User | null {
    const stored = localStorage.getItem('auth_user');
    return stored ? JSON.parse(stored) : null;
  }

  clearSession(): void {
    localStorage.removeItem('auth_user');
  }
}
```

#### 1.4 Create Encryption Utility (`src/utils/encryption.ts`)
```ts
import CryptoJS from 'crypto-js';

// Simple encryption key - in production, this should be more secure
const ENCRYPTION_KEY = 'electixir-transaction-key-2024';

export class EncryptionService {
  static encryptAmount(amount: number): string {
    const encrypted = CryptoJS.AES.encrypt(amount.toString(), ENCRYPTION_KEY).toString();
    return encrypted;
  }

  static decryptAmount(encryptedAmount: string): number {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedAmount, ENCRYPTION_KEY);
      const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);
      return parseInt(decryptedString, 10) || 0;
    } catch (error) {
      console.error('Failed to decrypt amount:', error);
      return 0;
    }
  }
}
```

### Phase 2: UI Updates (1-2 hours)

#### 2.1 Update App.tsx to Include Auth
```tsx
// Add to App.tsx
import { AuthProvider, useAuth } from './context/AuthContext';
import LoginScreen from './components/LoginScreen';

function AppContent() {
  const { state } = useAuth();

  if (!state.isAuthenticated) {
    return <LoginScreen />;
  }

  // Existing app content
  return (
    <>
      <Sidebar />
      <MainContent />
      <VoterDetailPanel />
    </>
  );
}

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <AppProvider>
          <DialogProvider>
            <AppContent />
          </DialogProvider>
        </AppProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
```

#### 2.2 Add Conditional Rendering
Update components to hide features based on user role:

```tsx
// In Toolbar.tsx - hide Add Voter for users
const { isAdmin } = useAuth();

{isAdmin() && (
  <button onClick={onAddVoter} className="btn btn-primary">
    Add Voter
  </button>
)}

// In VoterDetailPanel.tsx - hide transaction section for users
const { canAccessTransactions } = useAuth();

{canAccessTransactions() && (
  <section className="panel-section">
    {/* Transaction UI */}
  </section>
)}

// In SidebarFooter.tsx - hide Reports and Settings for users
{isAdmin() && (
  <a href="#" onClick={onReportsClick}>Reports</a>
)}
{isAdmin() && (
  <a href="#" onClick={onSettingsClick}>Settings</a>
)}
```

#### 2.3 Update Transaction Service for Encryption
```ts
// Update src/database/TransactionService.ts
import { EncryptionService } from '../utils/encryption';

export class TransactionService extends BaseService {
  // ... existing code ...

  public async addTransaction(transaction: Omit<Transaction, 'id' | 'created_at'>): Promise<number> {
    // Encrypt the amount before storing
    const encryptedAmount = EncryptionService.encryptAmount(transaction.amount);

    return this.executeTransaction(async () => {
      const insertId = this.executeInsert(
        'INSERT INTO transactions (voter_id, date, purpose, amount_encrypted) VALUES (?, ?, ?, ?)',
        [
          transaction.voter_id,
          transaction.date,
          transaction.purpose,
          encryptedAmount, // Store encrypted amount
        ]
      );
      return insertId;
    }, 'Add Transaction');
  }

  public async getTransactionsForVoter(voterId: number): Promise<Transaction[]> {
    const results = this.executeQuery<any>(
      'SELECT * FROM transactions WHERE voter_id = ? ORDER BY date DESC',
      [voterId]
    );

    // Decrypt amounts when retrieving
    return results.map(row => ({
      id: row.id,
      voter_id: row.voter_id,
      date: row.date,
      purpose: row.purpose,
      amount: EncryptionService.decryptAmount(row.amount_encrypted), // Decrypt amount
      created_at: row.created_at,
    }));
  }

  // Update other methods similarly...
}
```

#### 2.4 Add User Management to Settings
```tsx
// Add to SettingsDashboard.tsx
import { AuthService } from '../services/AuthService';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [newUsername, setNewUsername] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [authService] = useState(() => new AuthService());

  const handleAddUser = async () => {
    if (!newUsername.trim() || !newPassword.trim()) {
      alert('Please enter username and password');
      return;
    }

    const success = await authService.createUser(newUsername.trim(), newPassword);
    if (success) {
      setNewUsername('');
      setNewPassword('');
      loadUsers(); // Refresh user list
      alert('User created successfully');
    } else {
      alert('Failed to create user');
    }
  };

  return (
    <div className="user-management">
      <h3>User Management</h3>
      <div className="add-user-form">
        <input
          placeholder="Username"
          value={newUsername}
          onChange={(e) => setNewUsername(e.target.value)}
        />
        <input
          type="password"
          placeholder="Password (min 6 characters)"
          value={newPassword}
          onChange={(e) => setNewPassword(e.target.value)}
          minLength={6}
        />
        <button onClick={handleAddUser}>Add User</button>
      </div>
      <div className="user-list">
        {users.map(user => (
          <div key={user.id} className="user-item">
            <span>{user.username} ({user.role})</span>
            {user.username !== 'admin' && (
              <button onClick={() => handleDeleteUser(user.id)}>Delete</button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
```

#### 2.5 Initialize Auth Service in Database
```ts
// Update src/database/DatabaseService.ts
import { AuthService } from '../services/AuthService';

export class DatabaseService {
  // ... existing code ...

  public async initialize(config: DatabaseConfig = {}): Promise<void> {
    // ... existing initialization code ...

    // Create tables if they don't exist
    await this.createTables();

    // Ensure default admin user exists
    const authService = new AuthService();
    await authService.ensureDefaultAdmin();

    this.isInitialized = true;
  }
}
```

## File Structure
```
src/
├── context/
│   └── AuthContext.tsx          (NEW)
├── components/
│   ├── LoginScreen.tsx          (NEW)
│   └── UserManagement.tsx       (NEW)
├── services/
│   └── AuthService.ts           (NEW)
├── utils/
│   └── encryption.ts            (NEW)
└── database/
    └── TransactionService.ts    (UPDATED)
```

## Security Features

### Password Security
- **bcrypt hashing**: 10 salt rounds for secure password storage
- **No plain text**: Passwords never stored in plain text
- **Secure comparison**: Uses bcrypt.compare for validation

### Transaction Encryption
- **AES encryption**: All transaction amounts encrypted with crypto-js
- **Automatic handling**: Encrypt on save, decrypt on load
- **Transparent to UI**: Components work with decrypted values

## User Roles

### Admin
- Full access to everything
- Can add/remove users (except cannot delete admin)
- Can see all data including encrypted transactions
- Default credentials: admin/mawhati8123

### User
- Read-only access to voter data
- Cannot see transactions at all
- Cannot access reports or settings
- Cannot add/edit/delete voters

## Implementation Priority

1. ✅ Install bcryptjs and crypto-js dependencies
2. ✅ Update database schema for password hashes and encrypted amounts
3. ✅ Create enhanced AuthService with bcrypt
4. ✅ Add encryption utility for transactions
5. ✅ Update TransactionService for encryption
6. ✅ Create AuthContext and login screen
7. ✅ Add conditional rendering to hide features
8. ✅ Add user management to settings

## Quick Start Commands

```bash
# Install new dependencies
npm install bcryptjs crypto-js
npm install --save-dev @types/bcryptjs @types/crypto-js

# Then implement the components and update database schema
```

## Testing

1. Login as admin (admin/mawhati8123)
2. Create a regular user with 6+ character password
3. Logout and login as user
4. Verify transactions are completely hidden
5. Verify cannot edit voters or access settings
6. Check that transaction amounts are encrypted in database

## Security Notes

- **bcrypt**: Industry-standard password hashing with salt
- **AES encryption**: Symmetric encryption for transaction amounts
- **Offline security**: Suitable for personal use with trusted users
- **UI-based access control**: Hides sensitive features from regular users
- **Admin protection**: Cannot delete the admin user

## Default Users

- **Admin**: username=`admin`, password=`mawhati8123` (bcrypt hashed)
- Users can only be created by admin through the UI
- Minimum password length: 6 characters

This enhanced approach provides proper security while maintaining simplicity for personal offline use.
