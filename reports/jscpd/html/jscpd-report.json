{"statistics": {"detectionDate": "2025-07-25T12:55:38.754Z", "formats": {"tsx": {"sources": {"/Users/<USER>/Desktop/html/electixir/src/components/icons/SettingsIcon.tsx": {"lines": 18, "tokens": 134, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/icons/ReportsIcon.tsx": {"lines": 20, "tokens": 132, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/icons/ManageDataIcon.tsx": {"lines": 21, "tokens": 144, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/icons/LogoutIcon.tsx": {"lines": 21, "tokens": 144, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/icons/ImportCsvIcon.tsx": {"lines": 21, "tokens": 144, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/icons/ExportPdfIcon.tsx": {"lines": 23, "tokens": 168, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/icons/ChevronRightIcon.tsx": {"lines": 18, "tokens": 116, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/context/ThemeContext.tsx": {"lines": 89, "tokens": 696, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/context/AppContext.tsx": {"lines": 457, "tokens": 3579, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/StatsCards.tsx": {"lines": 131, "tokens": 1158, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/SidebarFooter.tsx": {"lines": 152, "tokens": 1418, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/Sidebar.tsx": {"lines": 296, "tokens": 2523, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/SettingsDashboard.tsx": {"lines": 266, "tokens": 2220, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/PollingStationNode.tsx": {"lines": 75, "tokens": 608, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/PollingStationList.tsx": {"lines": 49, "tokens": 334, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/MainContent.tsx": {"lines": 214, "tokens": 1777, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/FilterPanel.tsx": {"lines": 158, "tokens": 1320, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/DialogProvider.tsx": {"lines": 82, "tokens": 640, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/Dialog.tsx": {"lines": 181, "tokens": 1462, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/DataTable.tsx": {"lines": 325, "tokens": 2886, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/main.tsx": {"lines": 8, "tokens": 64, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/App.tsx": {"lines": 26, "tokens": 162, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 2651, "tokens": 21829, "sources": 22, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "typescript": {"sources": {"/Users/<USER>/Desktop/html/electixir/src/services/ExportService.ts": {"lines": 273, "tokens": 2504, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/hooks/usePollingStations.ts": {"lines": 162, "tokens": 1490, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/hooks/useLocalStorage.ts": {"lines": 65, "tokens": 534, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/hooks/useLightSweep.ts": {"lines": 28, "tokens": 172, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/hooks/useFilteredVoters.ts": {"lines": 128, "tokens": 1060, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/hooks/useExport.ts": {"lines": 77, "tokens": 906, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/hooks/useDialog.ts": {"lines": 116, "tokens": 891, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/hooks/useDebounce.ts": {"lines": 49, "tokens": 334, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/hooks/useDatabase.ts": {"lines": 405, "tokens": 3083, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/hooks/useColumnVisibility.ts": {"lines": 48, "tokens": 356, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/hooks/useClickOutside.ts": {"lines": 43, "tokens": 328, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/database/index.ts": {"lines": 17, "tokens": 185, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/database/TransactionService.ts": {"lines": 200, "tokens": 1399, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/database/SettingsService.ts": {"lines": 224, "tokens": 1556, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/database/FamilyService.ts": {"lines": 283, "tokens": 2107, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/database/DatabaseService.ts": {"lines": 327, "tokens": 1799, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/database/CSVImportService.ts": {"lines": 371, "tokens": 2975, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/test-database.ts": {"lines": 69, "tokens": 508, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/database-viewer.ts": {"lines": 214, "tokens": 1849, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/database-utils.ts": {"lines": 137, "tokens": 856, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 3236, "tokens": 24892, "sources": 20, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "javascript": {"sources": {"/Users/<USER>/Desktop/html/electixir/src/context/ThemeContext.tsx": {"lines": 0, "tokens": 3, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/context/AppContext.tsx": {"lines": 0, "tokens": 3, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/SidebarFooter.tsx": {"lines": 70, "tokens": 779, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/Sidebar.tsx": {"lines": 22, "tokens": 136, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/SettingsDashboard.tsx": {"lines": 94, "tokens": 832, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/PollingStationNode.tsx": {"lines": 24, "tokens": 257, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/PollingStationList.tsx": {"lines": 4, "tokens": 29, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/MainContent.tsx": {"lines": 97, "tokens": 897, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/FilterPanel.tsx": {"lines": 116, "tokens": 976, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/DialogProvider.tsx": {"lines": 21, "tokens": 156, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/Dialog.tsx": {"lines": 126, "tokens": 1051, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Desktop/html/electixir/src/components/DataTable.tsx": {"lines": 176, "tokens": 1609, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 750, "tokens": 6728, "sources": 12, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}}, "total": {"lines": 6637, "tokens": 53449, "sources": 54, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "duplicates": [], "filename": "/Users/<USER>/.npm/_npx/652ead47bf63fe99/node_modules/@jscpd/html-reporter/dist/templates/main.pug"}