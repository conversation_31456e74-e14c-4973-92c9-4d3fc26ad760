// ESLint configuration for TypeScript React project
export default [
  {
    ignores: [
      'dist/**',
      'node_modules/**',
      'src-tauri/target/**',
      'src-tauri/gen/**',
      'src-html/**',
      '*.config.js',
      '*.config.ts'
    ]
  },
  {
    files: ['**/*.{ts,tsx,js,jsx}'],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        }
      }
    },
    rules: {
      // Basic JavaScript/TypeScript rules
      'no-unused-vars': 'off', // Handled by TypeScript
      'no-console': 'warn',
      'no-debugger': 'error',
      'no-duplicate-imports': 'error',
      'prefer-const': 'error',
      'no-var': 'error',

      // React specific rules (basic)
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',
    }
  }
];
